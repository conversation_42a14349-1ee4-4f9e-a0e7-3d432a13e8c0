{"version": 3, "sources": ["../src/grants.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport type { RoomConfiguration } from '@livekit/protocol';\nimport { TrackSource } from '@livekit/protocol';\nimport type { JWTPayload } from 'jose';\n\nexport function trackSourceToString(source: TrackSource) {\n  switch (source) {\n    case TrackSource.CAMERA:\n      return 'camera';\n    case TrackSource.MICROPHONE:\n      return 'microphone';\n    case TrackSource.SCREEN_SHARE:\n      return 'screen_share';\n    case TrackSource.SCREEN_SHARE_AUDIO:\n      return 'screen_share_audio';\n    default:\n      throw new TypeError(`Cannot convert TrackSource ${source} to string`);\n  }\n}\n\nexport function claimsToJwtPayload(\n  grant: ClaimGrants,\n): JWTPayload & { video?: Record<string, unknown> } {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const claim: Record<string, any> = { ...grant };\n  // eslint-disable-next-line no-restricted-syntax\n  if (Array.isArray(claim.video?.canPublishSources)) {\n    claim.video.canPublishSources = claim.video.canPublishSources.map(trackSourceToString);\n  }\n  return claim;\n}\n\nexport interface VideoGrant {\n  /** permission to create a room */\n  roomCreate?: boolean;\n\n  /** permission to join a room as a participant, room must be set */\n  roomJoin?: boolean;\n\n  /** permission to list rooms */\n  roomList?: boolean;\n\n  /** permission to start a recording */\n  roomRecord?: boolean;\n\n  /** permission to control a specific room, room must be set */\n  roomAdmin?: boolean;\n\n  /** name of the room, must be set for admin or join permissions */\n  room?: string;\n\n  /** permissions to control ingress, not specific to any room or ingress */\n  ingressAdmin?: boolean;\n\n  /**\n   * allow participant to publish. If neither canPublish or canSubscribe is set,\n   * both publish and subscribe are enabled\n   */\n  canPublish?: boolean;\n\n  /**\n   * TrackSource types that the participant is allowed to publish\n   * When set, it supersedes CanPublish. Only sources explicitly set here can be published\n   */\n  canPublishSources?: TrackSource[];\n\n  /** allow participant to subscribe to other tracks */\n  canSubscribe?: boolean;\n\n  /**\n   * allow participants to publish data, defaults to true if not set\n   */\n  canPublishData?: boolean;\n\n  /**\n   * by default, a participant is not allowed to update its own metadata\n   */\n  canUpdateOwnMetadata?: boolean;\n\n  /** participant isn't visible to others */\n  hidden?: boolean;\n\n  /** participant is recording the room, when set, allows room to indicate it's being recorded */\n  recorder?: boolean;\n\n  /** participant allowed to connect to LiveKit as Agent Framework worker */\n  agent?: boolean;\n\n  /** allow participant to subscribe to metrics */\n  canSubscribeMetrics?: boolean;\n\n  /** destination room which this participant can forward to */\n  destinationRoom?: string;\n}\n\nexport interface SIPGrant {\n  /** manage sip resources */\n  admin?: boolean;\n\n  /** make outbound calls */\n  call?: boolean;\n}\n\n/** @internal */\nexport interface ClaimGrants extends JWTPayload {\n  name?: string;\n  video?: VideoGrant;\n  sip?: SIPGrant;\n  kind?: string;\n  metadata?: string;\n  attributes?: Record<string, string>;\n  sha256?: string;\n  roomPreset?: string;\n  roomConfig?: RoomConfiguration;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAA4B;AAGrB,SAAS,oBAAoB,QAAqB;AACvD,UAAQ,QAAQ;AAAA,IACd,KAAK,4BAAY;AACf,aAAO;AAAA,IACT,KAAK,4BAAY;AACf,aAAO;AAAA,IACT,KAAK,4BAAY;AACf,aAAO;AAAA,IACT,KAAK,4BAAY;AACf,aAAO;AAAA,IACT;AACE,YAAM,IAAI,UAAU,8BAA8B,MAAM,YAAY;AAAA,EACxE;AACF;AAEO,SAAS,mBACd,OACkD;AAxBpD;AA0BE,QAAM,QAA6B,EAAE,GAAG,MAAM;AAE9C,MAAI,MAAM,SAAQ,WAAM,UAAN,mBAAa,iBAAiB,GAAG;AACjD,UAAM,MAAM,oBAAoB,MAAM,MAAM,kBAAkB,IAAI,mBAAmB;AAAA,EACvF;AACA,SAAO;AACT;", "names": []}