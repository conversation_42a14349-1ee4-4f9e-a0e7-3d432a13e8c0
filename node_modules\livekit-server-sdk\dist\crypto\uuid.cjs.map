{"version": 3, "sources": ["../../src/crypto/uuid.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\n\n// Use the Web Crypto API if available, otherwise fallback to Node.js crypto\nexport async function getRandomBytes(size: number = 16): Promise<Uint8Array> {\n  if (globalThis.crypto) {\n    return crypto.getRandomValues(new Uint8Array(size));\n  } else {\n    const nodeCrypto = await import('node:crypto');\n    return nodeCrypto.getRandomValues(new Uint8Array(size));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,eAAsB,eAAe,OAAe,IAAyB;AAC3E,MAAI,WAAW,QAAQ;AACrB,WAAO,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC;AAAA,EACpD,OAAO;AACL,UAAM,aAAa,MAAM,OAAO,aAAa;AAC7C,WAAO,WAAW,gBAAgB,IAAI,WAAW,IAAI,CAAC;AAAA,EACxD;AACF;", "names": []}