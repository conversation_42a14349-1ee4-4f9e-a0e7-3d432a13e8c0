# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@bufbuild/protobuf@npm:^1.10.0, @bufbuild/protobuf@npm:^1.7.2":
  version: 1.10.1
  resolution: "@bufbuild/protobuf@npm:1.10.1"
  checksum: 10c0/a89572ae99aa193dd232fca0cdc9ece1dfe2f3d8b061be1f966a4f88fb63410aeb0fe7de927037e970aefcb52036eec58a7f89a40fe1286eed1448ea1bd2634e
  languageName: node
  linkType: hard

"@livekit/protocol@npm:^1.39.0":
  version: 1.39.3
  resolution: "@livekit/protocol@npm:1.39.3"
  dependencies:
    "@bufbuild/protobuf": "npm:^1.10.0"
  checksum: 10c0/192ce7283db4505232aa487e71d08670783a1045a8935926f95ff4b9d3d310db706c2ad4af2ebdb0bd735c2e9042c1ee3b1f62e44a9d01bd41fd58cf8dc272b9
  languageName: node
  linkType: hard

"camelcase-keys@npm:^9.0.0":
  version: 9.1.3
  resolution: "camelcase-keys@npm:9.1.3"
  dependencies:
    camelcase: "npm:^8.0.0"
    map-obj: "npm:5.0.0"
    quick-lru: "npm:^6.1.1"
    type-fest: "npm:^4.3.2"
  checksum: 10c0/ed8cb24f4b69f9be67b4a104250d9e025c5968a9a15e7e162b1ba3ecac5117abb106efd9484c93c497d0b0f6f0e5bc7bd4cedbb4e6f64ee115b3a49340886d60
  languageName: node
  linkType: hard

"camelcase@npm:^8.0.0":
  version: 8.0.0
  resolution: "camelcase@npm:8.0.0"
  checksum: 10c0/56c5fe072f0523c9908cdaac21d4a3b3fb0f608fb2e9ba90a60e792b95dd3bb3d1f3523873ab17d86d146e94171305f73ef619e2f538bd759675bc4a14b4bff3
  languageName: node
  linkType: hard

"jose@npm:^5.1.2":
  version: 5.10.0
  resolution: "jose@npm:5.10.0"
  checksum: 10c0/e20d9fc58d7e402f2e5f04e824b8897d5579aae60e64cb88ebdea1395311c24537bf4892f7de413fab1acf11e922797fb1b42269bc8fc65089a3749265ccb7b0
  languageName: node
  linkType: hard

"landing-page@workspace:.":
  version: 0.0.0-use.local
  resolution: "landing-page@workspace:."
  dependencies:
    livekit-server-sdk: "npm:^2.13.1"
  languageName: unknown
  linkType: soft

"livekit-server-sdk@npm:^2.13.1":
  version: 2.13.1
  resolution: "livekit-server-sdk@npm:2.13.1"
  dependencies:
    "@bufbuild/protobuf": "npm:^1.7.2"
    "@livekit/protocol": "npm:^1.39.0"
    camelcase-keys: "npm:^9.0.0"
    jose: "npm:^5.1.2"
  checksum: 10c0/4fe4e801a718201422cb4f905b18f7dca90ed2e03b3f8bd13c395cc3b2ae2593f7138473e17ebc10ad3743b67e46abb1c04e9558ff7f643e28492c913186fcdb
  languageName: node
  linkType: hard

"map-obj@npm:5.0.0":
  version: 5.0.0
  resolution: "map-obj@npm:5.0.0"
  checksum: 10c0/8ae0d8a3ce085e3e9eb46d7a4eba03681ffc644920046f7ba8edff37f11d30b0de4dd10e83f492ea6d3ba3b9c51de66d7ca6580c24b7e15d61b845d2f9f688ca
  languageName: node
  linkType: hard

"quick-lru@npm:^6.1.1":
  version: 6.1.2
  resolution: "quick-lru@npm:6.1.2"
  checksum: 10c0/f499f07bd276eec460c4d7d2ee286c519f3bd189cbbb5ddf3eb929e2182e4997f66b951ea8d24b3f3cee8ed5ac9f0006bf40636f082acd1b38c050a4cbf07ed3
  languageName: node
  linkType: hard

"type-fest@npm:^4.3.2":
  version: 4.41.0
  resolution: "type-fest@npm:4.41.0"
  checksum: 10c0/f5ca697797ed5e88d33ac8f1fec21921839871f808dc59345c9cf67345bfb958ce41bd821165dbf3ae591cedec2bf6fe8882098dfdd8dc54320b859711a2c1e4
  languageName: node
  linkType: hard
