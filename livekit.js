import { SipClient } from 'livekit-server-sdk';

const sipClient = new SipClient('wss://loma-vyn5765a.livekit.cloud',
    'APIggS5CXUXtwWk',
    'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC');

// Outbound trunk to use for the call
const trunkId = 'ST_pxFYJVBYt5cd';

// Phone number to dial
const phoneNumber = '+84938709344';

// Name of the room to attach the call to
const roomName = 'my-sip-room';

const sipParticipantOptions = {
    participantIdentity: 'sip-test',
    participantName: 'Test Caller',
    krispEnabled: true,
    waitUntilAnswered: true
};

async function main() {
    try {
        const participant = await sipClient.createSipParticipant(
            trunkId,
            phoneNumber,
            roomName,
            sipParticipantOptions
        );

        console.log('Participant created:', participant);
    } catch (error) {
        console.error('Error creating SIP participant:', error);
    }
}

main();