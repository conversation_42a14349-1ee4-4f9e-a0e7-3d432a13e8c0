// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_cloud_agent.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3, Timestamp } from "@bufbuild/protobuf";

/**
 * @generated from message livekit.AgentSecret
 */
export const AgentSecret = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentSecret",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "value", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
    { no: 3, name: "created_at", kind: "message", T: Timestamp },
    { no: 4, name: "updated_at", kind: "message", T: Timestamp },
  ],
);

/**
 * @generated from message livekit.CreateAgentRequest
 */
export const CreateAgentRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateAgentRequest",
  () => [
    { no: 1, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
    { no: 3, name: "replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "max_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "cpu_req", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "regions", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.CreateAgentResponse
 */
export const CreateAgentResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateAgentResponse",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "presigned_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.AgentDeployment
 */
export const AgentDeployment = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentDeployment",
  () => [
    { no: 1, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "min_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 6, name: "max_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 7, name: "cpu_req", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "cur_cpu", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "cur_mem", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "mem_req", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "mem_limit", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "cpu_limit", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.AgentInfo
 */
export const AgentInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentInfo",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "agent_deployments", kind: "message", T: AgentDeployment, repeated: true },
    { no: 5, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
    { no: 6, name: "deployed_at", kind: "message", T: Timestamp },
  ],
);

/**
 * @generated from message livekit.ListAgentsRequest
 */
export const ListAgentsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentsRequest",
  () => [
    { no: 1, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentsResponse
 */
export const ListAgentsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentsResponse",
  () => [
    { no: 1, name: "agents", kind: "message", T: AgentInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.AgentVersion
 */
export const AgentVersion = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentVersion",
  () => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "current", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "created_at", kind: "message", T: Timestamp },
  ],
);

/**
 * @generated from message livekit.ListAgentVersionsRequest
 */
export const ListAgentVersionsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentVersionsRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentVersionsResponse
 */
export const ListAgentVersionsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentVersionsResponse",
  () => [
    { no: 1, name: "versions", kind: "message", T: AgentVersion, repeated: true },
  ],
);

/**
 * @generated from message livekit.UpdateAgentRequest
 */
export const UpdateAgentRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateAgentRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "max_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "cpu_req", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "regions", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 7, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
  ],
);

/**
 * @generated from message livekit.UpdateAgentResponse
 */
export const UpdateAgentResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateAgentResponse",
  () => [
    { no: 1, name: "success", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeployAgentRequest
 */
export const DeployAgentRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeployAgentRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
    { no: 4, name: "replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "max_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 6, name: "cpu_req", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeployAgentResponse
 */
export const DeployAgentResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeployAgentResponse",
  () => [
    { no: 1, name: "success", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "presigned_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.UpdateAgentSecretsRequest
 */
export const UpdateAgentSecretsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateAgentSecretsRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "overwrite", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
  ],
);

/**
 * @generated from message livekit.UpdateAgentSecretsResponse
 */
export const UpdateAgentSecretsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateAgentSecretsResponse",
  () => [
    { no: 1, name: "success", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RollbackAgentRequest
 */
export const RollbackAgentRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RollbackAgentRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RollbackAgentResponse
 */
export const RollbackAgentResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RollbackAgentResponse",
  () => [
    { no: 1, name: "success", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeleteAgentRequest
 */
export const DeleteAgentRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteAgentRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeleteAgentResponse
 */
export const DeleteAgentResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteAgentResponse",
  () => [
    { no: 1, name: "success", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentSecretsRequest
 */
export const ListAgentSecretsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentSecretsRequest",
  () => [
    { no: 1, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentSecretsResponse
 */
export const ListAgentSecretsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentSecretsResponse",
  () => [
    { no: 1, name: "secrets", kind: "message", T: AgentSecret, repeated: true },
  ],
);

/**
 * @generated from message livekit.SettingsParam
 */
export const SettingsParam = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SettingsParam",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "value", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ClientSettingsResponse
 */
export const ClientSettingsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ClientSettingsResponse",
  () => [
    { no: 1, name: "params", kind: "message", T: SettingsParam, repeated: true },
  ],
);

/**
 * @generated from message livekit.ClientSettingsRequest
 */
export const ClientSettingsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ClientSettingsRequest",
  [],
);

