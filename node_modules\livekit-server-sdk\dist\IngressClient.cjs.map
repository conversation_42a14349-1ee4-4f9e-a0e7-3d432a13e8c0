{"version": 3, "sources": ["../src/IngressClient.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport type { IngressAudioOptions, IngressInput, IngressVideoOptions } from '@livekit/protocol';\nimport {\n  CreateIngressRequest,\n  DeleteIngressRequest,\n  IngressInfo,\n  ListIngressRequest,\n  ListIngressResponse,\n  UpdateIngressRequest,\n} from '@livekit/protocol';\nimport { ServiceBase } from './ServiceBase.js';\nimport type { Rpc } from './TwirpRPC.js';\nimport { TwirpRpc, livekitPackage } from './TwirpRPC.js';\n\nconst svc = 'Ingress';\n\nexport interface CreateIngressOptions {\n  /**\n   * ingress name. optional\n   */\n  name?: string;\n  /**\n   * name of the room to send media to. required\n   */\n  roomName?: string;\n  /**\n   * unique identity of the participant. required\n   */\n  participantIdentity: string;\n  /**\n   * participant display name\n   */\n  participantName?: string;\n  /**\n   * metadata to attach to the participant\n   */\n  participantMetadata?: string;\n  /**\n   * @deprecated use `enableTranscoding` instead.\n   * whether to skip transcoding and forward the input media directly. Only supported by WHIP\n   */\n  bypassTranscoding?: boolean;\n  /**\n   * whether to enable transcoding or forward the input media directly.\n   * Transcoding is required for all input types except WHIP. For WHIP, the default is to not transcode.\n   */\n  enableTranscoding?: boolean | undefined;\n  /**\n   * url of the media to pull for ingresses of type URL\n   */\n  url?: string;\n  /**\n   * custom audio encoding parameters. optional\n   */\n  audio?: IngressAudioOptions;\n  /**\n   * custom video encoding parameters. optional\n   */\n  video?: IngressVideoOptions;\n}\n\nexport interface UpdateIngressOptions {\n  /**\n   * ingress name. optional\n   */\n  name: string;\n  /**\n   * name of the room to send media to.\n   */\n  roomName?: string;\n  /**\n   * unique identity of the participant.\n   */\n  participantIdentity?: string;\n  /**\n   * participant display name\n   */\n  participantName?: string;\n  /**\n   * metadata to attach to the participant\n   */\n  participantMetadata?: string;\n  /**\n   * @deprecated use `enableTranscoding` instead\n   * whether to skip transcoding and forward the input media directly. Only supported by WHIP\n   */\n  bypassTranscoding?: boolean | undefined;\n  /**\n   * whether to enable transcoding or forward the input media directly.\n   * Transcoding is required for all input types except WHIP. For WHIP, the default is to not transcode.\n   */\n  enableTranscoding?: boolean | undefined;\n  /**\n   * custom audio encoding parameters. optional\n   */\n  audio?: IngressAudioOptions;\n  /**\n   * custom video encoding parameters. optional\n   */\n  video?: IngressVideoOptions;\n}\n\nexport interface ListIngressOptions {\n  /**\n   * list ingress for one room only\n   */\n  roomName?: string;\n\n  /**\n   * list ingress by ID\n   */\n  ingressId?: string;\n}\n\n/**\n * Client to access Ingress APIs\n */\nexport class IngressClient extends ServiceBase {\n  private readonly rpc: Rpc;\n\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host: string, apiKey?: string, secret?: string) {\n    super(apiKey, secret);\n    this.rpc = new TwirpRpc(host, livekitPackage);\n  }\n\n  /**\n   * @param inputType - protocol for the ingress\n   * @param opts - CreateIngressOptions\n   */\n  async createIngress(inputType: IngressInput, opts: CreateIngressOptions): Promise<IngressInfo> {\n    let name: string = '';\n    let participantName: string = '';\n    let participantIdentity: string = '';\n    let bypassTranscoding: boolean = false;\n    let url: string = '';\n\n    if (opts == null) {\n      throw new Error('options dictionary is required');\n    }\n\n    const roomName: string | undefined = opts.roomName;\n    const enableTranscoding: boolean | undefined = opts.enableTranscoding;\n    const audio: IngressAudioOptions | undefined = opts.audio;\n    const video: IngressVideoOptions | undefined = opts.video;\n    const participantMetadata: string | undefined = opts.participantMetadata;\n\n    name = opts.name || '';\n    participantName = opts.participantName || '';\n    participantIdentity = opts.participantIdentity || '';\n    bypassTranscoding = opts.bypassTranscoding || false;\n    url = opts.url || '';\n\n    if (typeof roomName == 'undefined') {\n      throw new Error('required roomName option not provided');\n    }\n\n    if (participantIdentity == '') {\n      throw new Error('required participantIdentity option not provided');\n    }\n\n    const req = new CreateIngressRequest({\n      inputType,\n      name,\n      roomName,\n      participantIdentity,\n      participantMetadata,\n      participantName,\n      bypassTranscoding,\n      enableTranscoding,\n      url,\n      audio,\n      video,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateIngress',\n      req,\n      await this.authHeader({ ingressAdmin: true }),\n    );\n    return IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param ingressId - ID of the ingress to update\n   * @param opts - UpdateIngressOptions\n   */\n  async updateIngress(ingressId: string, opts: UpdateIngressOptions): Promise<IngressInfo> {\n    const name: string = opts.name || '';\n    const roomName: string = opts.roomName || '';\n    const participantName: string = opts.participantName || '';\n    const participantIdentity: string = opts.participantIdentity || '';\n    const { participantMetadata } = opts;\n    const { audio, video, bypassTranscoding, enableTranscoding } = opts;\n\n    const req = new UpdateIngressRequest({\n      ingressId,\n      name,\n      roomName,\n      participantIdentity,\n      participantName,\n      participantMetadata,\n      bypassTranscoding,\n      enableTranscoding,\n      audio,\n      video,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateIngress',\n      req,\n      await this.authHeader({ ingressAdmin: true }),\n    );\n    return IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @deprecated use `listIngress(opts)` or `listIngress(arg)` instead\n   * @param roomName - list ingress for one room only\n   */\n  async listIngress(roomName?: string): Promise<Array<IngressInfo>>;\n  /**\n   * @param opts - list options\n   */\n  async listIngress(opts?: ListIngressOptions): Promise<Array<IngressInfo>>;\n  /**\n   * @param arg - list room name or options\n   */\n  async listIngress(arg?: string | ListIngressOptions): Promise<Array<IngressInfo>> {\n    let req: Partial<ListIngressRequest> = {};\n    if (typeof arg === 'string') {\n      req.roomName = arg;\n    } else if (arg) {\n      req = arg;\n    }\n    const data = await this.rpc.request(\n      svc,\n      'ListIngress',\n      new ListIngressRequest(req).toJson(),\n      await this.authHeader({ ingressAdmin: true }),\n    );\n    return ListIngressResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * @param ingressId - ingress to delete\n   */\n  async deleteIngress(ingressId: string): Promise<IngressInfo> {\n    const data = await this.rpc.request(\n      svc,\n      'DeleteIngress',\n      new DeleteIngressRequest({ ingressId }).toJson(),\n      await this.authHeader({ ingressAdmin: true }),\n    );\n    return IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAOO;AACP,yBAA4B;AAE5B,sBAAyC;AAEzC,MAAM,MAAM;AAuGL,MAAM,sBAAsB,+BAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7C,YAAY,MAAc,QAAiB,QAAiB;AAC1D,UAAM,QAAQ,MAAM;AACpB,SAAK,MAAM,IAAI,yBAAS,MAAM,8BAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc,WAAyB,MAAkD;AAC7F,QAAI,OAAe;AACnB,QAAI,kBAA0B;AAC9B,QAAI,sBAA8B;AAClC,QAAI,oBAA6B;AACjC,QAAI,MAAc;AAElB,QAAI,QAAQ,MAAM;AAChB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AAEA,UAAM,WAA+B,KAAK;AAC1C,UAAM,oBAAyC,KAAK;AACpD,UAAM,QAAyC,KAAK;AACpD,UAAM,QAAyC,KAAK;AACpD,UAAM,sBAA0C,KAAK;AAErD,WAAO,KAAK,QAAQ;AACpB,sBAAkB,KAAK,mBAAmB;AAC1C,0BAAsB,KAAK,uBAAuB;AAClD,wBAAoB,KAAK,qBAAqB;AAC9C,UAAM,KAAK,OAAO;AAElB,QAAI,OAAO,YAAY,aAAa;AAClC,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AAEA,QAAI,uBAAuB,IAAI;AAC7B,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACpE;AAEA,UAAM,MAAM,IAAI,qCAAqB;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,cAAc,KAAK,CAAC;AAAA,IAC9C;AACA,WAAO,4BAAY,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc,WAAmB,MAAkD;AACvF,UAAM,OAAe,KAAK,QAAQ;AAClC,UAAM,WAAmB,KAAK,YAAY;AAC1C,UAAM,kBAA0B,KAAK,mBAAmB;AACxD,UAAM,sBAA8B,KAAK,uBAAuB;AAChE,UAAM,EAAE,oBAAoB,IAAI;AAChC,UAAM,EAAE,OAAO,OAAO,mBAAmB,kBAAkB,IAAI;AAE/D,UAAM,MAAM,IAAI,qCAAqB;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,cAAc,KAAK,CAAC;AAAA,IAC9C;AACA,WAAO,4BAAY,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,YAAY,KAAgE;AAChF,QAAI,MAAmC,CAAC;AACxC,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,WAAW;AAAA,IACjB,WAAW,KAAK;AACd,YAAM;AAAA,IACR;AACA,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,mCAAmB,GAAG,EAAE,OAAO;AAAA,MACnC,MAAM,KAAK,WAAW,EAAE,cAAc,KAAK,CAAC;AAAA,IAC9C;AACA,WAAO,oCAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,WAAyC;AAC3D,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,qCAAqB,EAAE,UAAU,CAAC,EAAE,OAAO;AAAA,MAC/C,MAAM,KAAK,WAAW,EAAE,cAAc,KAAK,CAAC;AAAA,IAC9C;AACA,WAAO,4BAAY,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACjE;AACF;", "names": []}