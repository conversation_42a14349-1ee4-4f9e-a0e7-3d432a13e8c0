<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loma Bag - Xưởng Sản Xuất Túi Vải In Logo Theo <PERSON></title>
    <meta
      name="description"
      content="Xưởng sản xuất túi vải in logo trực tiếp, không qua trung gian. Nhận sản xuất từ 50 túi. <PERSON><PERSON><PERSON> phí thiết kế, gi<PERSON> gốc tận xưởng. Giải pháp marketing hiệu quả cho thương hiệu."
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Be Vietnam Pro", sans-serif;
        background-color: #f9fafb;
        line-height: 1.7;
      }
      h1, h2, h3, h4, h5, h6 {
        line-height: 1.4;
      }
      .cta-button {
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }
      .cta-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
      }
      .hero-image-container {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 1rem;
        height: 100%;
        max-height: 500px;
      }
      .hero-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.75rem;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
      }
      .hero-image-container img:hover {
        transform: scale(1.05);
        z-index: 10;
      }
    </style>
  </head>
  <body class="text-gray-800">
    <!-- HEADER (Tương tự phiên bản cũ) -->
    <header class="bg-white/95 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm border-b border-gray-100">
      <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="#" class="flex items-center">
              <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//logo-full.png" alt="Loma Bag Logo" class="h-10">
            </a>
            <span class="ml-2 md:ml-3 bg-red-100 text-red-600 text-xs font-bold px-2 py-1 rounded-full">Xưởng Sản Xuất</span>
          </div>
          <div class="hidden md:flex items-center space-x-3 lg:space-x-4">
            <div class="hidden lg:flex items-center text-sm text-gray-600">
              <a href="https://zalo.me/0938069715" target="_blank" class="flex items-center hover:text-blue-600 transition-colors">
                <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//zalo-icon.png" alt="Zalo" class="w-4 h-4 mr-1">
                <span class="font-semibold">0938069715</span>
              </a>
            </div>
            <a href="#form-bao-gia" class="cta-button bg-red-500 text-white font-bold py-2 px-4 lg:px-6 rounded-full hover:bg-red-600 text-sm">
              🎁 Báo Giá Nhanh
            </a>
          </div>
          <a href="#form-bao-gia" class="md:hidden cta-button bg-red-500 text-white font-bold py-2 px-4 rounded-full hover:bg-red-600 text-sm">
            Báo Giá
          </a>
        </div>
      </div>
    </header>

    <!-- NEW HERO SECTION -->
    <section class="bg-gradient-to-b from-red-50 to-white pt-32 pb-20">
      <div class="container mx-auto px-6">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <!-- Cột Nội dung & Form -->
          <div class="text-center lg:text-left">
            <span class="inline-block bg-red-100 text-red-700 font-semibold px-4 py-1 rounded-full mb-4 text-sm">
              Xưởng Sản Xuất Trực Tiếp - Không Qua Trung Gian
            </span>
            <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 leading-tight mb-5">
              Túi Vải In Logo Theo Yêu Cầu
              <span class="text-red-500">Nâng Tầm Thương Hiệu</span>
            </h1>
            <p class="text-lg text-gray-600 mb-8 max-w-xl mx-auto lg:mx-0">
              Chúng tôi giúp hơn 500+ thương hiệu tạo ra những chiếc túi vải độc đáo, chất lượng, tối ưu chi phí để làm quà tặng khách hàng, đóng gói sản phẩm và marketing hiệu quả.
            </p>
            
            <div class="space-y-3 text-left mb-8 inline-block">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <span class="text-gray-700"><strong>Giá gốc tận xưởng,</strong> tiết kiệm đến 30% chi phí.</span>
              </div>
              <div class="flex items-center">
                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <span class="text-gray-700"><strong>Miễn phí thiết kế & duyệt mẫu</strong> đến khi bạn hài lòng.</span>
              </div>
              <div class="flex items-center">
                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <span class="text-gray-700"><strong>Sản xuất nhanh chỉ từ 3-5 ngày,</strong> nhận đơn từ 50 túi.</span>
              </div>
            </div>

            <div id="form-bao-gia" class="mt-6 bg-white p-6 rounded-2xl shadow-lg border border-gray-100">
                <h3 class="font-bold text-xl text-center mb-4">Nhận báo giá chi tiết và thiết kế miễn phí ngay!</h3>
                <form action="#" method="POST" class="space-y-4">
                    <div>
                        <label for="phone" class="sr-only">Số điện thoại</label>
                        <input type="tel" name="phone" id="phone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500" placeholder="Nhập SĐT của bạn (ưu tiên Zalo)" required>
                    </div>
                    <div>
                        <label for="quantity" class="sr-only">Số lượng dự kiến</label>
                        <input type="number" name="quantity" id="quantity" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500" placeholder="Số lượng túi dự kiến (ví dụ: 100)">
                    </div>
                    <button type="submit" class="cta-button w-full bg-red-500 text-white font-bold py-4 px-8 rounded-lg text-lg hover:bg-red-600">
                      GỬI YÊU CẦU TƯ VẤN
                    </button>
                </form>
                <p class="text-xs text-gray-500 mt-3 text-center">Chúng tôi sẽ liên hệ lại qua Zalo để tư vấn và gửi mẫu thiết kế.</p>
            </div>
          </div>

          <!-- Cột Hình ảnh sản phẩm -->
          <div class="hidden lg:grid hero-image-container">
            <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-quai-phoi-mau-loma-bag-1.jpg" alt="Mẫu túi vải canvas in logo" class="row-span-2">
            <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-vai-bo-in-logo/tui-vai-bo-tron-co-day-rut-in-logo-loma-bag.jpg" alt="Mẫu túi vải bố dây rút">
            <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-khong-det-in-logo/tui-vai-khong-det-gap-gon-in-logo-loma-bag.jpg" alt="Mẫu túi vải không dệt">
          </div>
        </div>
      </div>
    </section>

    <!-- Các section khác của trang web có thể đặt ở đây -->

  </body>
</html>