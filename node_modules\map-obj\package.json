{"name": "map-obj", "version": "5.0.0", "description": "Map object keys and values into a new object", "license": "MIT", "repository": "sindresorhus/map-obj", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["map", "object", "key", "keys", "value", "values", "iterate", "iterator", "rename", "modify", "deep", "recurse", "recursive"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}}