import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// C<PERSON>u hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// C<PERSON>u hình cuộc gọi
const phoneNumber = '+84938709344';  // Số điện thoại cần gọi
const roomName = 'stringee-test-room';

// Tùy chọn cho SIP participant
const participantOptions = {
    participantIdentity: 'stringee-caller-' + Date.now(),
    participantName: 'Stringee Test Caller',
    participantMetadata: 'Stringee outbound call test',
    krispEnabled: false,  // Tắt Krisp để giảm complexity
    waitUntilAnswered: false,  // Không đợi answer để test nhanh hơn
    playDialtone: true,  // Bật dial tone để nghe thấy
    hidePhoneNumber: false
};

// Hàm test với timeout ngắn hơn
async function testStringeeCall() {
    console.log('=== Stringee SIP Test ===');
    console.log('LiveKit URL:', livekitUrl);
    console.log('Target phone:', phoneNumber);
    console.log('Room:', roomName);
    console.log('');

    try {
        // Lấy thông tin trunk Stringee
        console.log('🔍 Getting Stringee trunk info...');
        const trunks = await sipClient.listSipOutboundTrunk();
        const stringeeTrunk = trunks.find(t => t.name === 'stringee');
        
        if (!stringeeTrunk) {
            console.error('❌ Stringee trunk not found');
            return;
        }

        console.log('✅ Found Stringee trunk:');
        console.log('- ID:', stringeeTrunk.sipTrunkId);
        console.log('- Address:', stringeeTrunk.address);
        console.log('- Numbers:', stringeeTrunk.numbers);
        console.log('- Username:', stringeeTrunk.authUsername);
        console.log('- Password:', stringeeTrunk.authPassword ? '***set***' : 'NOT SET');
        console.log('');

        // Kiểm tra cấu hình trunk
        if (!stringeeTrunk.authPassword) {
            console.error('⚠️  WARNING: Auth password not set for Stringee trunk');
            console.log('💡 You may need to update the trunk with correct Stringee credentials');
        }

        // Tạo room trước để đảm bảo room tồn tại
        console.log('🏠 Creating room...');
        try {
            await roomService.createRoom({
                name: roomName,
                emptyTimeout: 300, // 5 minutes
                maxParticipants: 10
            });
            console.log('✅ Room created successfully');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ Room already exists');
            } else {
                console.log('⚠️  Room creation warning:', error.message);
            }
        }

        // Test cuộc gọi với timeout settings
        console.log('📞 Initiating Stringee call...');
        console.log('⏱️  This may take 30-60 seconds...');
        
        const startTime = Date.now();
        
        const participant = await sipClient.createSipParticipant(
            stringeeTrunk.sipTrunkId,
            phoneNumber,
            roomName,
            participantOptions
        );

        const duration = Date.now() - startTime;
        
        console.log('🎉 SUCCESS! Call initiated in', duration, 'ms');
        console.log('');
        console.log('📋 Call Details:');
        console.log('- Participant ID:', participant.participantId);
        console.log('- Identity:', participant.participantIdentity);
        console.log('- Room:', participant.roomName);
        console.log('- SIP Call ID:', participant.sipCallId);
        console.log('');
        console.log('📞 Call Status:');
        console.log('- The call should now be dialing', phoneNumber);
        console.log('- Check your phone for incoming call');
        console.log('- Join room', roomName, 'in LiveKit to hear the call');
        
        return participant;

    } catch (error) {
        console.error('❌ Stringee call failed:');
        console.error('Error:', error.message);
        
        // Phân tích lỗi cụ thể
        if (error.message.includes('timeout')) {
            console.log('');
            console.log('🔍 Timeout Analysis:');
            console.log('- This usually means Stringee server is not responding');
            console.log('- Possible causes:');
            console.log('  • Stringee account inactive or out of credit');
            console.log('  • Wrong authentication credentials');
            console.log('  • Network connectivity issues');
            console.log('  • Stringee server maintenance');
            console.log('');
            console.log('💡 Next Steps:');
            console.log('1. Check Stringee account balance and status');
            console.log('2. Verify trunk credentials in LiveKit Cloud');
            console.log('3. Test with a different SIP provider (Twilio/Telnyx)');
            
        } else if (error.message.includes('unauthorized')) {
            console.log('');
            console.log('🔐 Authentication Issue:');
            console.log('- Stringee credentials are incorrect');
            console.log('- Update trunk with correct username/password');
            
        } else {
            console.log('');
            console.log('🔍 Unknown Error:');
            console.log('- This might be a configuration issue');
            console.log('- Check LiveKit and Stringee documentation');
        }
        
        return null;
    }
}

// Hàm hiển thị hướng dẫn Stringee
function showStringeeGuide() {
    console.log('');
    console.log('📖 Stringee Configuration Guide:');
    console.log('');
    console.log('1. Login to Stringee Dashboard: https://developer.stringee.com/');
    console.log('2. Check account balance and status');
    console.log('3. Verify SIP credentials:');
    console.log('   - SIP Domain: v2.stringee.com:15060');
    console.log('   - Username: Your Stringee SIP username');
    console.log('   - Password: Your Stringee SIP password');
    console.log('4. Update LiveKit trunk if needed');
    console.log('');
    console.log('🔗 Useful Links:');
    console.log('- Stringee Docs: https://developer.stringee.com/docs');
    console.log('- LiveKit Cloud: https://cloud.livekit.io/');
    console.log('');
}

// Main execution
async function main() {
    const participant = await testStringeeCall();
    
    if (!participant) {
        showStringeeGuide();
    }
    
    console.log('✅ Test completed');
}

console.log('🚀 Starting Stringee SIP test...');
main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
});
