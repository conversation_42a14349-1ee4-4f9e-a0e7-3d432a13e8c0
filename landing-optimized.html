<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loma Bag - Túi Vải In Logo Giúp Tăng 15-30% Tỷ Lệ Chốt Đơn</title>
    <meta name="description" content="Xưởng sản xuất túi vải in logo từ 50 túi. Giúp shop online tăng tỷ lệ chốt đơn, tăng giá trị đơn hàng. Miễn phí thiết kế mockup, cọc 500k hoàn lại 100%.">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SwiperJS for Gallery Slider -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <style>
        /* Custom Styles */
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
            background-color: #f9fafb;
            line-height: 1.7;
        }
        h1, h2, h3, h4, h5, h6 {
            line-height: 1.4;
        }
        .cta-button {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }
        .section-title {
            position: relative;
            padding-bottom: 1rem;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #EF4444;
            border-radius: 2px;
        }

        /* Enhanced Hero Section */
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        .hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        /* Typing animation */
        .typed-cursor {
            opacity: 1;
            animation: blink 0.7s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* Floating animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        /* Calculator Tool Custom Range Input */
        .calculator-tool input[type="range"] {
            -webkit-appearance: none; appearance: none;
            width: 100%; height: 8px; background: #d1d5db;
            border-radius: 5px; outline: none; opacity: 0.7;
            transition: opacity .2s; position: relative;
        }
        .calculator-tool input[type="range"]:hover { opacity: 1; }
        .calculator-tool input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none; appearance: none;
            width: 24px; height: 24px; background: #EF4444;
            cursor: pointer; border-radius: 50%; box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        .calculator-tool input[type="range"]::-moz-range-thumb {
            width: 24px; height: 24px; background: #EF4444;
            cursor: pointer; border-radius: 50%; border: none; box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        /* Range progress bar */
        .range-container {
            position: relative;
            height: 8px;
        }
        .range-progress {
            position: absolute;
            height: 8px;
            background: #EF4444;
            border-radius: 5px;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1;
        }
        .range-container input[type="range"] {
            position: relative;
            z-index: 2;
            background: transparent;
        }
        /* Currency input formatting */
        .currency-input {
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- HEADER -->
    <header class="bg-white/90 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="#" class="flex items-center">
              <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma//logo-full.png" alt="Loma Bag Logo" class="h-10">
            </a>
            <a href="#form-bao-gia" class="cta-button bg-red-500 text-white font-bold py-2 px-6 rounded-full hover:bg-red-600 hidden sm:inline-block">
                Nhận Báo Giá Chính Xác
            </a>
        </div>
    </header>

    <!-- HERO SECTION -->
    <section class="hero-gradient text-white pt-32 pb-20 text-center relative">
        <div class="container mx-auto px-6 relative z-10">
            <!-- Floating icons -->
            <div class="absolute top-10 left-10 float-animation hidden lg:block">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <span class="text-2xl">🛍️</span>
                </div>
            </div>
            <div class="absolute top-20 right-20 float-animation hidden lg:block" style="animation-delay: 1s;">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <span class="text-xl">📈</span>
                </div>
            </div>

            <div class="max-w-4xl mx-auto">
                <h2 class="text-4xl md:text-6xl font-extrabold leading-tight mb-6">
                    Đang chạy Ads mà khách cứ "xem rồi thoát"?<br>
                    <span class="text-yellow-300">Thêm 1 món quà nhỏ để...</span><br>
                    <span id="typing-effect" class="text-red-300"></span>
                </h2>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
                    <p class="text-xl md:text-2xl font-semibold text-yellow-100 mb-4">
                        💡 Sự thật: Bạn đã chi tiền cho Ads để khách click vào
                    </p>
                    <p class="text-lg md:text-xl text-white/90">
                        Nhưng <strong class="text-yellow-300">90% khách xem rồi thoát</strong> mà không mua gì.
                        Một chiếc <strong class="text-red-300">túi vải in logo làm quà tặng</strong> sẽ là "cú hích" cuối cùng
                        khiến họ <strong class="text-green-300">chốt đơn ngay lập tức!</strong>
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
                     <a href="#calculator-tool" class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block w-full sm:w-auto">
                        🧮 Tính Ngay Chi Phí Đầu Tư
                    </a>
                    <a href="#case-study" class="cta-button bg-white/20 backdrop-blur-sm text-white border border-white/30 font-bold py-4 px-8 rounded-full text-lg hover:bg-white/30 inline-block w-full sm:w-auto">
                        📊 Xem Case Thực Tế
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- CALCULATOR TOOL SECTION -->
    <section id="calculator-tool" class="py-16 md:py-24 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-2xl md:text-4xl font-bold">🧮 Tính Ngay: Nên Đầu Tư Bao Nhiêu Cho Túi Vải?</h3>
                <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
                    Nhập thông tin shop của bạn → Tính toán số tiền đầu tư hợp lý cho 1 túi và ước tính số lượng cần đặt.
                    <strong class="text-red-600">Công thức đã được kiểm chứng bởi 500+ shop!</strong>
                </p>
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-4xl mx-auto">
                    <p class="text-sm text-blue-800">
                        <strong>💡 Hướng dẫn:</strong> Điều chỉnh các thông số để tìm mức đầu tư phù hợp với ngân sách và mục tiêu của bạn.
                    </p>
                </div>
            </div>
            <div class="calculator-tool max-w-6xl mx-auto bg-white p-4 sm:p-8 rounded-xl shadow-2xl grid lg:grid-cols-2 gap-6 lg:gap-12">
                <!-- Inputs -->
                <div class="space-y-4 sm:space-y-6">
                    <h4 class="text-xl sm:text-2xl font-bold text-gray-800 border-b pb-2">📊 Thông tin shop của bạn</h4>
                    <div>
                        <label for="currentAOV" class="block font-semibold text-sm sm:text-base mb-1">🛒 Giá trị đơn hàng trung bình (VND)</label>
                        <input type="text" id="currentAOV" value="500,000" class="currency-input w-full p-2 sm:p-3 border border-gray-300 rounded-md text-base sm:text-lg font-bold focus:ring-2 focus:ring-red-500 focus:border-red-500 text-left" placeholder="500,000">
                        <p class="text-xs text-gray-500 mt-1">💡 Gợi ý: Thời trang 300-800k, mỹ phẩm 200-600k, phụ kiện 150-400k</p>
                    </div>
                    <div>
                        <label for="monthlyOrders" class="block font-semibold text-sm sm:text-base mb-1">📦 Số đơn hàng/tháng hiện tại</label>
                        <input type="number" id="monthlyOrders" value="100" class="w-full p-2 sm:p-3 border border-gray-300 rounded-md text-base sm:text-lg font-bold focus:ring-2 focus:ring-red-500 focus:border-red-500 text-left" placeholder="100">
                        <p class="text-xs text-gray-500 mt-1">💡 Gợi ý: Shop nhỏ 50-200 đơn, shop vừa 200-500 đơn, shop lớn 500+ đơn</p>
                    </div>

                    <h4 class="text-xl sm:text-2xl font-bold text-gray-800 border-b pb-2 pt-4">🎯 Mục tiêu với túi quà</h4>
                    <div>
                        <label for="giftCondition" class="block font-semibold text-sm sm:text-base mb-1">💰 Điều kiện tặng túi (VND)</label>
                        <input type="text" id="giftCondition" value="800,000" class="currency-input w-full p-2 sm:p-3 border border-gray-300 rounded-md text-base sm:text-lg font-bold focus:ring-2 focus:ring-red-500 focus:border-red-500 text-left" placeholder="800,000">
                        <p class="text-xs text-gray-500 mt-1">💡 Thường đặt cao hơn AOV hiện tại 50-100k để khuyến khích mua thêm</p>
                    </div>
                    <div>
                        <label for="qualifiedPercent" class="block font-semibold text-sm sm:text-base mb-2">📈 % đơn đạt điều kiện tặng túi (dự kiến)</label>
                        <div class="flex items-center">
                            <div class="range-container flex-1 relative">
                                <div class="range-progress" id="qualifiedProgress"></div>
                                <input type="range" id="qualifiedPercent" min="10" max="80" value="30" class="w-full">
                            </div>
                            <span id="qualifiedPercentValue" class="ml-3 text-base sm:text-lg font-bold w-12 sm:w-16 text-center text-red-600">30%</span>
                        </div>
                        <p class="text-xs sm:text-sm text-gray-500 mt-1">💡 Tùy thuộc điều kiện và sức hút của túi (thường 20-50%)</p>
                    </div>
                    <div>
                        <label for="bagCost" class="block font-semibold text-sm sm:text-base mb-1">🎁 Chi phí 1 túi (VND)</label>
                        <input type="text" id="bagCost" value="35,000" class="currency-input w-full p-2 sm:p-3 border border-gray-300 rounded-md text-base sm:text-lg font-bold focus:ring-2 focus:ring-red-500 focus:border-red-500 text-left" placeholder="35,000">
                        <p class="text-xs text-gray-500 mt-1">💡 Gợi ý: Túi cơ bản 25-35k, túi đẹp 35-50k, túi cao cấp 50-70k</p>
                    </div>
                </div>

                <!-- Outputs -->
                <div class="bg-gradient-to-br from-blue-50 to-green-50 p-4 sm:p-6 rounded-lg border border-blue-200">
                    <h4 class="text-xl sm:text-2xl font-bold text-gray-800 mb-4">📈 Kết quả tính toán</h4>
                    <div class="space-y-3 sm:space-y-4">
                        <div class="p-3 bg-white/70 rounded-md border border-gray-200">
                            <p class="text-xs sm:text-sm font-semibold text-gray-600">Số túi cần tặng/tháng</p>
                            <p id="bagsNeeded" class="text-lg sm:text-xl font-bold text-blue-600">30 túi</p>
                        </div>
                        <div class="p-3 bg-white/70 rounded-md border border-gray-200">
                            <p class="text-xs sm:text-sm font-semibold text-gray-600">Tổng chi phí túi/tháng</p>
                            <p id="totalMonthlyCost" class="text-lg sm:text-xl font-bold text-red-600">1.050.000đ</p>
                        </div>
                        <div class="p-3 bg-white/70 rounded-md border border-gray-200">
                            <p class="text-xs sm:text-sm font-semibold text-gray-600">Chi phí túi/đơn hàng trung bình</p>
                            <p id="costPerOrder" class="text-lg sm:text-xl font-bold text-orange-600">10.500đ</p>
                        </div>
                        <div class="p-3 bg-white/70 rounded-md border border-gray-200">
                            <p class="text-xs sm:text-sm font-semibold text-gray-600">% chi phí túi so với AOV</p>
                            <p id="costPercentage" class="text-lg sm:text-xl font-bold text-purple-600">2.1%</p>
                        </div>
                    </div>

                    <div class="mt-4 sm:mt-6 bg-blue-100 border-2 border-blue-500 text-blue-800 p-3 sm:p-4 rounded-lg text-center">
                        <p class="font-semibold uppercase text-sm sm:text-base">💡 Gợi ý số lượng đặt</p>
                        <p id="suggestedQuantity" class="text-2xl sm:text-4xl font-extrabold text-blue-600 mt-1">100 túi</p>
                        <p class="text-xs sm:text-sm text-blue-700 mt-1">Đủ dùng khoảng <span id="monthsSupply">3 tháng</span></p>
                    </div>

                    <!-- Explanation Box -->
                    <div class="mt-4 p-4 bg-blue-50 border border-blue-300 rounded-lg">
                        <h5 class="font-semibold text-blue-800 mb-2">🧮 Cách tính đơn giản:</h5>
                        <div class="text-xs sm:text-sm text-blue-700 space-y-1">
                            <p><strong>Bước 1:</strong> Số túi cần tặng = Số đơn/tháng × % đơn đạt điều kiện</p>
                            <p><strong>Bước 2:</strong> Chi phí/tháng = Số túi cần tặng × Chi phí 1 túi</p>
                            <p><strong>Bước 3:</strong> Chi phí/đơn = Chi phí/tháng ÷ Tổng số đơn/tháng</p>
                            <p class="text-green-700 font-semibold">✅ Kết quả: Mức đầu tư hợp lý cho túi vải!</p>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-300 rounded-lg">
                        <p class="text-xs sm:text-sm text-yellow-800">
                            💡 <strong>Lưu ý:</strong> Đây là ước tính dựa trên kinh nghiệm thực tế của 500+ shop khách hàng.
                            Nên bắt đầu với số lượng nhỏ để test hiệu quả trước khi scale up.
                        </p>
                    </div>

                    <!-- Additional Benefits -->
                    <div class="mt-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg">
                        <h5 class="font-semibold text-purple-800 mb-2">🎁 Lợi ích khác KHÔNG tính được bằng tiền:</h5>
                        <div class="text-xs sm:text-sm text-purple-700 space-y-1">
                            <p>• <strong>Quảng bá thương hiệu:</strong> Khách dùng túi → Người khác thấy logo → Tăng nhận diện thương hiệu</p>
                            <p>• <strong>Lý do tương tác lại:</strong> "Chị ơi, túi em tặng hôm trước có thích không? Em có thêm mẫu mới..."</p>
                            <p>• <strong>Bám đuổi khách chưa mua:</strong> "Anh đã xem sản phẩm, mua ngay để nhận túi đẹp miễn phí"</p>
                            <p>• <strong>Tăng độ trung thành:</strong> Khách cảm thấy được quan tâm → Mua lại nhiều lần hơn</p>
                            <p>• <strong>Giảm chi phí marketing:</strong> Thay vì giảm giá 50k → Tặng túi 35k có giá trị cao hơn</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FORM SECTION -->
    <section id="form-bao-gia" class="py-16 md:py-24 bg-gradient-to-br from-gray-800 to-gray-900 text-white">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="max-w-3xl mx-auto">
                <div class="text-center mb-10">
                    <h3 class="text-2xl md:text-4xl font-bold mb-4">🚀 Bắt Đầu Ngay - Nhận Mockup Miễn Phí!</h3>
                    <p class="text-lg md:text-xl text-gray-300 mb-6">
                        Chỉ cần 2 phút để điền form → Nhận thiết kế mockup túi với logo của bạn trong vòng 2 giờ!
                    </p>
                    <div class="flex flex-wrap justify-center gap-4 text-sm">
                        <span class="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">✅ Mockup miễn phí</span>
                        <span class="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">✅ Tư vấn qua Zalo</span>
                        <span class="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">✅ Báo giá chính xác</span>
                    </div>
                </div>

                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-white/20">
                    <form action="#" method="POST" class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">👤 Tên của bạn *</label>
                                <input type="text" name="name" id="name" required
                                       class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                                       placeholder="Ví dụ: Nguyễn Văn A">
                            </div>
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">📱 Số Zalo *</label>
                                <input type="tel" name="phone" id="phone" required
                                       class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                                       placeholder="0901234567">
                            </div>
                        </div>

                        <div>
                            <label for="business" class="block text-sm font-medium text-gray-300 mb-2">🏪 Bạn bán gì? *</label>
                            <select name="business" id="business" required
                                    class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white">
                                <option value="">Chọn ngành hàng của bạn</option>
                                <option value="my-pham">Mỹ phẩm</option>
                                <option value="thoi-trang">Thời trang</option>
                                <option value="phu-kien">Phụ kiện</option>
                                <option value="do-gia-dung">Đồ gia dụng</option>
                                <option value="thuc-pham">Thực phẩm</option>
                                <option value="khac">Khác</option>
                            </select>
                        </div>

                        <div>
                            <label for="package" class="block text-sm font-medium text-gray-300 mb-2">📦 Bạn quan tâm gói nào? *</label>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <label class="flex items-center p-4 bg-white/5 rounded-lg border border-white/20 cursor-pointer hover:bg-white/10 transition-colors">
                                    <input type="radio" name="package" value="test" required class="mr-3 text-red-500">
                                    <div>
                                        <div class="font-semibold text-red-300">🎯 Gói Thử Nghiệm</div>
                                        <div class="text-sm text-gray-400">Mockup + Mẫu miễn phí</div>
                                    </div>
                                </label>
                                <label class="flex items-center p-4 bg-white/5 rounded-lg border border-white/20 cursor-pointer hover:bg-white/10 transition-colors">
                                    <input type="radio" name="package" value="scale" required class="mr-3 text-blue-500">
                                    <div>
                                        <div class="font-semibold text-blue-300">🚀 Gói Scale Up</div>
                                        <div class="text-sm text-gray-400">Giá sỉ, số lượng lớn</div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-300 mb-2">💭 Ghi chú thêm (không bắt buộc)</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                                      placeholder="Ví dụ: Muốn túi màu đỏ, in logo 2 mặt, dự kiến đặt 200 túi..."></textarea>
                        </div>

                        <div class="text-center pt-4">
                            <button type="submit" class="cta-button w-full bg-gradient-to-r from-red-500 to-red-600 text-white font-bold py-4 px-8 rounded-full text-lg hover:from-red-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300">
                                🎁 NHẬN MOCKUP MIỄN PHÍ NGAY
                            </button>
                            <p class="text-sm text-gray-400 mt-3">
                                ⚡ Phản hồi trong 2 giờ | 🔒 Thông tin được bảo mật
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://unpkg.com/typed.js@2.0.12"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- TYPING EFFECT ---
            new Typed('#typing-effect', {
                strings: [
                    'tăng 15-30% tỷ lệ chốt đơn?',
                    'tăng 10-25% giá trị đơn hàng?',
                    'khiến khách nhớ thương hiệu?',
                    'tạo lý do để khách mua thêm?',
                    'biến khách thành fan trung thành?'
                ],
                typeSpeed: 60,
                backSpeed: 40,
                backDelay: 1500,
                startDelay: 500,
                loop: true,
                smartBackspace: true,
                showCursor: true,
                cursorChar: '|'
            });

            // --- CURRENCY INPUT FORMATTING ---
            function formatCurrencyInput(value) {
                // Remove all non-digits
                const numericValue = value.replace(/[^\d]/g, '');
                // Add thousand separators
                return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }

            function parseCurrencyInput(value) {
                // Remove commas and convert to number
                return parseFloat(value.replace(/,/g, '')) || 0;
            }

            // --- RANGE PROGRESS BARS ---
            function updateRangeProgress(rangeInput, progressBar) {
                const value = rangeInput.value;
                const min = rangeInput.min || 0;
                const max = rangeInput.max || 100;
                const percentage = ((value - min) / (max - min)) * 100;
                progressBar.style.width = percentage + '%';
            }

            // --- CALCULATOR LOGIC ---
            const inputs = {
                currentAOV: document.getElementById('currentAOV'),
                monthlyOrders: document.getElementById('monthlyOrders'),
                giftCondition: document.getElementById('giftCondition'),
                qualifiedPercent: document.getElementById('qualifiedPercent'),
                bagCost: document.getElementById('bagCost'),
            };

            const outputs = {
                qualifiedPercentValue: document.getElementById('qualifiedPercentValue'),
                bagsNeeded: document.getElementById('bagsNeeded'),
                totalMonthlyCost: document.getElementById('totalMonthlyCost'),
                costPerOrder: document.getElementById('costPerOrder'),
                costPercentage: document.getElementById('costPercentage'),
                suggestedQuantity: document.getElementById('suggestedQuantity'),
                monthsSupply: document.getElementById('monthsSupply'),
            };

            function formatCurrency(num) {
                if (isNaN(num) || num === 0) return '0đ';
                // Format with dots as thousand separators
                return Math.round(num).toLocaleString('vi-VN') + 'đ';
            }

            function calculate() {
                // 1. Read values from inputs
                const currentAOV = parseCurrencyInput(inputs.currentAOV.value);
                const monthlyOrders = parseFloat(inputs.monthlyOrders.value) || 0;
                const giftCondition = parseCurrencyInput(inputs.giftCondition.value);
                const qualifiedPercent = parseFloat(inputs.qualifiedPercent.value) || 0;
                const bagCost = parseCurrencyInput(inputs.bagCost.value);

                // 2. Update slider display values and progress bars
                outputs.qualifiedPercentValue.textContent = `${qualifiedPercent}%`;
                updateRangeProgress(inputs.qualifiedPercent, document.getElementById('qualifiedProgress'));

                // 3. Perform calculations
                const bagsNeeded = Math.round(monthlyOrders * (qualifiedPercent / 100));
                const totalMonthlyCost = bagsNeeded * bagCost;
                const costPerOrder = monthlyOrders > 0 ? totalMonthlyCost / monthlyOrders : 0;
                const costPercentage = currentAOV > 0 ? (costPerOrder / currentAOV) * 100 : 0;

                // Suggested quantity calculation (round up to nearest 50 for orders under 100, nearest 100 for larger)
                let suggestedQuantity;
                if (bagsNeeded <= 50) {
                    suggestedQuantity = Math.max(50, Math.ceil(bagsNeeded / 25) * 25);
                } else if (bagsNeeded <= 200) {
                    suggestedQuantity = Math.ceil(bagsNeeded / 50) * 50;
                } else {
                    suggestedQuantity = Math.ceil(bagsNeeded / 100) * 100;
                }

                const monthsSupply = bagsNeeded > 0 ? (suggestedQuantity / bagsNeeded).toFixed(1) : 0;

                // 4. Update output elements
                outputs.bagsNeeded.textContent = `${bagsNeeded} túi`;
                outputs.totalMonthlyCost.textContent = formatCurrency(totalMonthlyCost);
                outputs.costPerOrder.textContent = formatCurrency(costPerOrder);
                outputs.costPercentage.textContent = `${costPercentage.toFixed(1)}%`;
                outputs.suggestedQuantity.textContent = `${suggestedQuantity} túi`;
                outputs.monthsSupply.textContent = `${monthsSupply} tháng`;
            }

            // Add event listeners for currency inputs
            [inputs.currentAOV, inputs.giftCondition, inputs.bagCost].forEach(input => {
                input.addEventListener('input', function(e) {
                    const formatted = formatCurrencyInput(e.target.value);
                    e.target.value = formatted;
                    calculate();
                });

                input.addEventListener('blur', function(e) {
                    // Ensure proper formatting on blur
                    const formatted = formatCurrencyInput(e.target.value);
                    e.target.value = formatted;
                });
            });

            // Add event listeners for number and range inputs
            [inputs.monthlyOrders, inputs.qualifiedPercent].forEach(input => {
                input.addEventListener('input', calculate);
            });

            // Initial calculation on page load
            calculate();
        });
    </script>

</body>
</html>