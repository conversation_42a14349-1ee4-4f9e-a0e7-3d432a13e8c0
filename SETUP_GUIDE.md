# LiveKit SIP Outbound Call Setup Guide

## Vấn đề hiện tại
Script đang gặp lỗi timeout vì chưa có **outbound trunk** đư<PERSON><PERSON> cấu hình đúng cách với SIP provider thực tế.

## C<PERSON><PERSON> bướ<PERSON> cần thực hiện

### 1. Đ<PERSON>ng ký SIP Provider
Bạn cần đăng ký với một trong các SIP provider sau:

#### **Twilio** (Khuyến nghị)
- Website: https://www.twilio.com/
- Ưu điểm: Dễ setup, documentation tốt
- Chi phí: ~$1/tháng cho số điện thoại + $0.0085/phút

#### **Telnyx**
- Website: https://telnyx.com/
- Ưu điểm: Giá rẻ hơn Twilio
- Chi phí: ~$0.40/tháng cho số điện thoại

#### **Plivo**
- Website: https://www.plivo.com/
- Ưu điểm: Hỗ trợ tốt cho châu Á

### 2. <PERSON><PERSON>u hình SIP Provider

#### Với Twilio:
1. <PERSON><PERSON>o tài khoản Twilio
2. Mua một số điện thoại
3. Tạo SIP Trunk trong Twilio Console
4. Cấu hình webhook URL: `https://your-livekit-domain.livekit.cloud/sip`
5. Lấy thông tin:
   - SIP Domain: `your-project.pstn.twilio.com`
   - Username: Twilio username
   - Password: Twilio password

#### Với Telnyx:
1. Tạo tài khoản Telnyx
2. Mua số điện thoại
3. Tạo SIP Connection
4. Lấy thông tin:
   - SIP Domain: `sip.telnyx.com`
   - Username: Connection username
   - Password: Connection password

### 3. Cấu hình LiveKit Cloud

1. Đăng nhập vào [LiveKit Cloud](https://cloud.livekit.io/)
2. Vào **Telephony** → **Configuration**
3. Tạo **Outbound Trunk**:

```json
{
  "name": "My Outbound Trunk",
  "address": "your-sip-provider-domain.com",
  "numbers": ["+**********"],
  "authUsername": "your-username",
  "authPassword": "your-password",
  "destinationCountry": "VN"
}
```

### 4. Cập nhật script

Sau khi có trunk thực tế, cập nhật `livekit.js`:

```javascript
// Thay đổi trunkId thành ID thực tế từ LiveKit Cloud
const trunkId = 'TR_your_real_trunk_id';

// Hoặc để script tự động lấy trunk đầu tiên
let trunkId = null; // Script sẽ tự động lấy
```

### 5. Test script

```bash
node livekit.js
```

## Cấu hình mẫu cho các provider

### Twilio Configuration
```json
{
  "name": "Twilio Outbound",
  "address": "your-project.pstn.twilio.com",
  "numbers": ["+15551234567"],
  "authUsername": "your-twilio-username",
  "authPassword": "your-twilio-password",
  "destinationCountry": "VN"
}
```

### Telnyx Configuration
```json
{
  "name": "Telnyx Outbound",
  "address": "sip.telnyx.com",
  "numbers": ["+15551234567"],
  "authUsername": "your-connection-id",
  "authPassword": "your-api-key",
  "destinationCountry": "VN"
}
```

## Troubleshooting

### Lỗi Timeout
- Kiểm tra internet connection
- Verify LiveKit credentials
- Đảm bảo trunk đã được tạo và active

### Lỗi Authentication
- Kiểm tra API key/secret
- Verify SIP provider credentials
- Đảm bảo token có quyền SIP call

### Lỗi Trunk Not Found
- Kiểm tra trunk ID có đúng không
- Verify trunk đã được tạo trong LiveKit Cloud
- Đảm bảo trunk có outbound calling enabled

## Chi phí ước tính

### Setup một lần:
- Twilio: $0 (free trial)
- Telnyx: $0 (free trial)
- LiveKit Cloud: Free tier available

### Chi phí hàng tháng:
- Số điện thoại: $1-2/tháng
- Cuộc gọi: $0.005-0.01/phút
- LiveKit: Free tier cho development

## Liên kết hữu ích

- [LiveKit SIP Documentation](https://docs.livekit.io/sip/)
- [Twilio SIP Trunk Setup](https://docs.livekit.io/sip/quickstarts/configuring-twilio-trunk/)
- [Telnyx SIP Trunk Setup](https://docs.livekit.io/sip/quickstarts/configuring-telnyx-trunk/)
- [LiveKit Cloud Dashboard](https://cloud.livekit.io/)

## Lưu ý quan trọng

⚠️ **Không thể test outbound call mà không có SIP provider thực tế!**

Script hiện tại chỉ có thể hoạt động khi:
1. Có SIP provider account (Twilio/Telnyx/Plivo)
2. Đã cấu hình outbound trunk trong LiveKit Cloud
3. Trunk được liên kết với SIP provider

Nếu chỉ muốn test code, hãy sử dụng Twilio free trial để setup nhanh.
