// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_cloud_agent.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message livekit.AgentSecret
 */
export declare class AgentSecret extends Message<AgentSecret> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: bytes value = 2;
   */
  value: Uint8Array;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 4;
   */
  updatedAt?: Timestamp;

  constructor(data?: PartialMessage<AgentSecret>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentSecret";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentSecret;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentSecret;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentSecret;

  static equals(a: AgentSecret | PlainMessage<AgentSecret> | undefined, b: AgentSecret | PlainMessage<AgentSecret> | undefined): boolean;
}

/**
 * @generated from message livekit.CreateAgentRequest
 */
export declare class CreateAgentRequest extends Message<CreateAgentRequest> {
  /**
   * @generated from field: string agent_name = 1 [deprecated = true];
   * @deprecated
   */
  agentName: string;

  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 2;
   */
  secrets: AgentSecret[];

  /**
   * @generated from field: int32 replicas = 3 [deprecated = true];
   * @deprecated
   */
  replicas: number;

  /**
   * @generated from field: int32 max_replicas = 4 [deprecated = true];
   * @deprecated
   */
  maxReplicas: number;

  /**
   * @generated from field: string cpu_req = 5 [deprecated = true];
   * @deprecated
   */
  cpuReq: string;

  /**
   * @generated from field: repeated string regions = 6;
   */
  regions: string[];

  constructor(data?: PartialMessage<CreateAgentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateAgentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAgentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAgentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAgentRequest;

  static equals(a: CreateAgentRequest | PlainMessage<CreateAgentRequest> | undefined, b: CreateAgentRequest | PlainMessage<CreateAgentRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.CreateAgentResponse
 */
export declare class CreateAgentResponse extends Message<CreateAgentResponse> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  /**
   * @generated from field: string status = 3;
   */
  status: string;

  /**
   * @generated from field: string version = 4;
   */
  version: string;

  /**
   * @generated from field: string presigned_url = 5;
   */
  presignedUrl: string;

  constructor(data?: PartialMessage<CreateAgentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateAgentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAgentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAgentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAgentResponse;

  static equals(a: CreateAgentResponse | PlainMessage<CreateAgentResponse> | undefined, b: CreateAgentResponse | PlainMessage<CreateAgentResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.AgentDeployment
 */
export declare class AgentDeployment extends Message<AgentDeployment> {
  /**
   * @generated from field: string region = 1;
   */
  region: string;

  /**
   * @generated from field: string agent_id = 2;
   */
  agentId: string;

  /**
   * @generated from field: string status = 3;
   */
  status: string;

  /**
   * @generated from field: int32 replicas = 4;
   */
  replicas: number;

  /**
   * @generated from field: int32 min_replicas = 5;
   */
  minReplicas: number;

  /**
   * @generated from field: int32 max_replicas = 6;
   */
  maxReplicas: number;

  /**
   * @generated from field: string cpu_req = 7;
   */
  cpuReq: string;

  /**
   * @generated from field: string cur_cpu = 8;
   */
  curCpu: string;

  /**
   * @generated from field: string cur_mem = 9;
   */
  curMem: string;

  /**
   * @generated from field: string mem_req = 10;
   */
  memReq: string;

  /**
   * @generated from field: string mem_limit = 11;
   */
  memLimit: string;

  /**
   * @generated from field: string cpu_limit = 12;
   */
  cpuLimit: string;

  constructor(data?: PartialMessage<AgentDeployment>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentDeployment";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentDeployment;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentDeployment;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentDeployment;

  static equals(a: AgentDeployment | PlainMessage<AgentDeployment> | undefined, b: AgentDeployment | PlainMessage<AgentDeployment> | undefined): boolean;
}

/**
 * @generated from message livekit.AgentInfo
 */
export declare class AgentInfo extends Message<AgentInfo> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  /**
   * @generated from field: string version = 3;
   */
  version: string;

  /**
   * @generated from field: repeated livekit.AgentDeployment agent_deployments = 4;
   */
  agentDeployments: AgentDeployment[];

  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 5;
   */
  secrets: AgentSecret[];

  /**
   * @generated from field: google.protobuf.Timestamp deployed_at = 6;
   */
  deployedAt?: Timestamp;

  constructor(data?: PartialMessage<AgentInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentInfo;

  static equals(a: AgentInfo | PlainMessage<AgentInfo> | undefined, b: AgentInfo | PlainMessage<AgentInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentsRequest
 */
export declare class ListAgentsRequest extends Message<ListAgentsRequest> {
  /**
   * @generated from field: string agent_name = 1;
   */
  agentName: string;

  /**
   * @generated from field: string agent_id = 2;
   */
  agentId: string;

  constructor(data?: PartialMessage<ListAgentsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentsRequest;

  static equals(a: ListAgentsRequest | PlainMessage<ListAgentsRequest> | undefined, b: ListAgentsRequest | PlainMessage<ListAgentsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentsResponse
 */
export declare class ListAgentsResponse extends Message<ListAgentsResponse> {
  /**
   * @generated from field: repeated livekit.AgentInfo agents = 1;
   */
  agents: AgentInfo[];

  constructor(data?: PartialMessage<ListAgentsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentsResponse;

  static equals(a: ListAgentsResponse | PlainMessage<ListAgentsResponse> | undefined, b: ListAgentsResponse | PlainMessage<ListAgentsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.AgentVersion
 */
export declare class AgentVersion extends Message<AgentVersion> {
  /**
   * @generated from field: string version = 1;
   */
  version: string;

  /**
   * @generated from field: bool current = 2;
   */
  current: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  constructor(data?: PartialMessage<AgentVersion>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentVersion";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentVersion;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentVersion;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentVersion;

  static equals(a: AgentVersion | PlainMessage<AgentVersion> | undefined, b: AgentVersion | PlainMessage<AgentVersion> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentVersionsRequest
 */
export declare class ListAgentVersionsRequest extends Message<ListAgentVersionsRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  constructor(data?: PartialMessage<ListAgentVersionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentVersionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentVersionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentVersionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentVersionsRequest;

  static equals(a: ListAgentVersionsRequest | PlainMessage<ListAgentVersionsRequest> | undefined, b: ListAgentVersionsRequest | PlainMessage<ListAgentVersionsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentVersionsResponse
 */
export declare class ListAgentVersionsResponse extends Message<ListAgentVersionsResponse> {
  /**
   * @generated from field: repeated livekit.AgentVersion versions = 1;
   */
  versions: AgentVersion[];

  constructor(data?: PartialMessage<ListAgentVersionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentVersionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentVersionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentVersionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentVersionsResponse;

  static equals(a: ListAgentVersionsResponse | PlainMessage<ListAgentVersionsResponse> | undefined, b: ListAgentVersionsResponse | PlainMessage<ListAgentVersionsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateAgentRequest
 */
export declare class UpdateAgentRequest extends Message<UpdateAgentRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2 [deprecated = true];
   * @deprecated
   */
  agentName: string;

  /**
   * @generated from field: int32 replicas = 3 [deprecated = true];
   * @deprecated
   */
  replicas: number;

  /**
   * @generated from field: int32 max_replicas = 4 [deprecated = true];
   * @deprecated
   */
  maxReplicas: number;

  /**
   * @generated from field: string cpu_req = 5 [deprecated = true];
   * @deprecated
   */
  cpuReq: string;

  /**
   * @generated from field: repeated string regions = 6;
   */
  regions: string[];

  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 7;
   */
  secrets: AgentSecret[];

  constructor(data?: PartialMessage<UpdateAgentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateAgentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAgentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAgentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAgentRequest;

  static equals(a: UpdateAgentRequest | PlainMessage<UpdateAgentRequest> | undefined, b: UpdateAgentRequest | PlainMessage<UpdateAgentRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateAgentResponse
 */
export declare class UpdateAgentResponse extends Message<UpdateAgentResponse> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<UpdateAgentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateAgentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAgentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAgentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAgentResponse;

  static equals(a: UpdateAgentResponse | PlainMessage<UpdateAgentResponse> | undefined, b: UpdateAgentResponse | PlainMessage<UpdateAgentResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeployAgentRequest
 */
export declare class DeployAgentRequest extends Message<DeployAgentRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2 [deprecated = true];
   * @deprecated
   */
  agentName: string;

  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 3;
   */
  secrets: AgentSecret[];

  /**
   * @generated from field: int32 replicas = 4 [deprecated = true];
   * @deprecated
   */
  replicas: number;

  /**
   * @generated from field: int32 max_replicas = 5 [deprecated = true];
   * @deprecated
   */
  maxReplicas: number;

  /**
   * @generated from field: string cpu_req = 6 [deprecated = true];
   * @deprecated
   */
  cpuReq: string;

  constructor(data?: PartialMessage<DeployAgentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeployAgentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeployAgentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeployAgentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeployAgentRequest;

  static equals(a: DeployAgentRequest | PlainMessage<DeployAgentRequest> | undefined, b: DeployAgentRequest | PlainMessage<DeployAgentRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.DeployAgentResponse
 */
export declare class DeployAgentResponse extends Message<DeployAgentResponse> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * @generated from field: string agent_id = 3;
   */
  agentId: string;

  /**
   * @generated from field: string presigned_url = 4;
   */
  presignedUrl: string;

  constructor(data?: PartialMessage<DeployAgentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeployAgentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeployAgentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeployAgentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeployAgentResponse;

  static equals(a: DeployAgentResponse | PlainMessage<DeployAgentResponse> | undefined, b: DeployAgentResponse | PlainMessage<DeployAgentResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateAgentSecretsRequest
 */
export declare class UpdateAgentSecretsRequest extends Message<UpdateAgentSecretsRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  /**
   * @generated from field: bool overwrite = 3;
   */
  overwrite: boolean;

  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 4;
   */
  secrets: AgentSecret[];

  constructor(data?: PartialMessage<UpdateAgentSecretsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateAgentSecretsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAgentSecretsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAgentSecretsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAgentSecretsRequest;

  static equals(a: UpdateAgentSecretsRequest | PlainMessage<UpdateAgentSecretsRequest> | undefined, b: UpdateAgentSecretsRequest | PlainMessage<UpdateAgentSecretsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateAgentSecretsResponse
 */
export declare class UpdateAgentSecretsResponse extends Message<UpdateAgentSecretsResponse> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<UpdateAgentSecretsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateAgentSecretsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAgentSecretsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAgentSecretsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAgentSecretsResponse;

  static equals(a: UpdateAgentSecretsResponse | PlainMessage<UpdateAgentSecretsResponse> | undefined, b: UpdateAgentSecretsResponse | PlainMessage<UpdateAgentSecretsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.RollbackAgentRequest
 */
export declare class RollbackAgentRequest extends Message<RollbackAgentRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  /**
   * @generated from field: string version = 3;
   */
  version: string;

  constructor(data?: PartialMessage<RollbackAgentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RollbackAgentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RollbackAgentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RollbackAgentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RollbackAgentRequest;

  static equals(a: RollbackAgentRequest | PlainMessage<RollbackAgentRequest> | undefined, b: RollbackAgentRequest | PlainMessage<RollbackAgentRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RollbackAgentResponse
 */
export declare class RollbackAgentResponse extends Message<RollbackAgentResponse> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<RollbackAgentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RollbackAgentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RollbackAgentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RollbackAgentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RollbackAgentResponse;

  static equals(a: RollbackAgentResponse | PlainMessage<RollbackAgentResponse> | undefined, b: RollbackAgentResponse | PlainMessage<RollbackAgentResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteAgentRequest
 */
export declare class DeleteAgentRequest extends Message<DeleteAgentRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  constructor(data?: PartialMessage<DeleteAgentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteAgentRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAgentRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAgentRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAgentRequest;

  static equals(a: DeleteAgentRequest | PlainMessage<DeleteAgentRequest> | undefined, b: DeleteAgentRequest | PlainMessage<DeleteAgentRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteAgentResponse
 */
export declare class DeleteAgentResponse extends Message<DeleteAgentResponse> {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<DeleteAgentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteAgentResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAgentResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAgentResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAgentResponse;

  static equals(a: DeleteAgentResponse | PlainMessage<DeleteAgentResponse> | undefined, b: DeleteAgentResponse | PlainMessage<DeleteAgentResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentSecretsRequest
 */
export declare class ListAgentSecretsRequest extends Message<ListAgentSecretsRequest> {
  /**
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  constructor(data?: PartialMessage<ListAgentSecretsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentSecretsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentSecretsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentSecretsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentSecretsRequest;

  static equals(a: ListAgentSecretsRequest | PlainMessage<ListAgentSecretsRequest> | undefined, b: ListAgentSecretsRequest | PlainMessage<ListAgentSecretsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentSecretsResponse
 */
export declare class ListAgentSecretsResponse extends Message<ListAgentSecretsResponse> {
  /**
   * @generated from field: repeated livekit.AgentSecret secrets = 1;
   */
  secrets: AgentSecret[];

  constructor(data?: PartialMessage<ListAgentSecretsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentSecretsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentSecretsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentSecretsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentSecretsResponse;

  static equals(a: ListAgentSecretsResponse | PlainMessage<ListAgentSecretsResponse> | undefined, b: ListAgentSecretsResponse | PlainMessage<ListAgentSecretsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.SettingsParam
 */
export declare class SettingsParam extends Message<SettingsParam> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string value = 2;
   */
  value: string;

  constructor(data?: PartialMessage<SettingsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SettingsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SettingsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SettingsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SettingsParam;

  static equals(a: SettingsParam | PlainMessage<SettingsParam> | undefined, b: SettingsParam | PlainMessage<SettingsParam> | undefined): boolean;
}

/**
 * @generated from message livekit.ClientSettingsResponse
 */
export declare class ClientSettingsResponse extends Message<ClientSettingsResponse> {
  /**
   * @generated from field: repeated livekit.SettingsParam params = 1;
   */
  params: SettingsParam[];

  constructor(data?: PartialMessage<ClientSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ClientSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientSettingsResponse;

  static equals(a: ClientSettingsResponse | PlainMessage<ClientSettingsResponse> | undefined, b: ClientSettingsResponse | PlainMessage<ClientSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.ClientSettingsRequest
 */
export declare class ClientSettingsRequest extends Message<ClientSettingsRequest> {
  constructor(data?: PartialMessage<ClientSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ClientSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientSettingsRequest;

  static equals(a: ClientSettingsRequest | PlainMessage<ClientSettingsRequest> | undefined, b: ClientSettingsRequest | PlainMessage<ClientSettingsRequest> | undefined): boolean;
}

