{"version": 3, "sources": ["../src/SipClient.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport { Duration } from '@bufbuild/protobuf';\nimport type {\n  ListUpdate,\n  Pagination,\n  RoomConfiguration,\n  SIPHeaderOptions,\n} from '@livekit/protocol';\nimport {\n  CreateSIPDispatchRuleRequest,\n  CreateSIPInboundTrunkRequest,\n  CreateSIPOutboundTrunkRequest,\n  CreateSIPParticipantRequest,\n  CreateSIPTrunkRequest,\n  DeleteSIPDispatchRuleRequest,\n  DeleteSIPTrunkRequest,\n  ListSIPDispatchRuleRequest,\n  ListSIPDispatchRuleResponse,\n  ListSIPInboundTrunkRequest,\n  ListSIPInboundTrunkResponse,\n  ListSIPOutboundTrunkRequest,\n  ListSIPOutboundTrunkResponse,\n  ListSIPTrunkRequest,\n  ListSIPTrunkResponse,\n  SIPDispatchRule,\n  SIPDispatchRuleDirect,\n  SIPDispatchRuleIndividual,\n  SIPDispatchRuleInfo,\n  SIPInboundTrunkInfo,\n  SIPOutboundTrunkInfo,\n  SIPParticipantInfo,\n  SIPTransport,\n  SIPTrunkInfo,\n  TransferSIPParticipantRequest,\n  UpdateSIPDispatchRuleRequest,\n  UpdateSIPInboundTrunkRequest,\n  UpdateSIPOutboundTrunkRequest,\n} from '@livekit/protocol';\nimport { ServiceBase } from './ServiceBase.js';\nimport type { Rpc } from './TwirpRPC.js';\nimport { TwirpRpc, livekitPackage } from './TwirpRPC.js';\n\nconst svc = 'SIP';\n\n/**\n * @deprecated use CreateSipInboundTrunkOptions or CreateSipOutboundTrunkOptions\n */\nexport interface CreateSipTrunkOptions {\n  name?: string;\n  metadata?: string;\n  inbound_addresses?: string[];\n  inbound_numbers?: string[];\n  inbound_username?: string;\n  inbound_password?: string;\n  outbound_address?: string;\n  outbound_username?: string;\n  outbound_password?: string;\n}\nexport interface CreateSipInboundTrunkOptions {\n  metadata?: string;\n  /** @deprecated - use `allowedAddresses` instead */\n  allowed_addresses?: string[];\n  allowedAddresses?: string[];\n  /** @deprecated - use `allowedNumbers` instead */\n  allowed_numbers?: string[];\n  allowedNumbers?: string[];\n  /** @deprecated - use `authUsername` instead */\n  auth_username?: string;\n  authUsername?: string;\n  /** @deprecated - use `authPassword` instead */\n  auth_password?: string;\n  authPassword?: string;\n  headers?: { [key: string]: string };\n  headersToAttributes?: { [key: string]: string };\n  // Map SIP response headers from INVITE to sip.h.* participant attributes automatically.\n  includeHeaders?: SIPHeaderOptions;\n  krispEnabled?: boolean;\n}\nexport interface CreateSipOutboundTrunkOptions {\n  metadata?: string;\n  transport: SIPTransport;\n  destinationCountry?: string;\n  /** @deprecated - use `authUsername` instead */\n  auth_username?: string;\n  authUsername?: string;\n  /** @deprecated - use `authPassword` instead */\n  auth_password?: string;\n  authPassword?: string;\n  headers?: { [key: string]: string };\n  headersToAttributes?: { [key: string]: string };\n  // Map SIP response headers from INVITE to sip.h.* participant attributes automatically.\n  includeHeaders?: SIPHeaderOptions;\n}\n\nexport interface SipDispatchRuleDirect {\n  type: 'direct';\n  roomName: string;\n  pin?: string;\n}\n\nexport interface SipDispatchRuleIndividual {\n  type: 'individual';\n  roomPrefix: string;\n  pin?: string;\n}\n\nexport interface CreateSipDispatchRuleOptions {\n  name?: string;\n  metadata?: string;\n  trunkIds?: string[];\n  hidePhoneNumber?: boolean;\n  attributes?: { [key: string]: string };\n  roomPreset?: string;\n  roomConfig?: RoomConfiguration;\n}\n\nexport interface CreateSipParticipantOptions {\n  /** Optional SIP From number to use. If empty, trunk number is used. */\n  fromNumber?: string;\n  /** Optional identity of the SIP participant */\n  participantIdentity?: string;\n  /** Optional name of the participant */\n  participantName?: string;\n  /** Optional metadata to attach to the participant */\n  participantMetadata?: string;\n  /** Optional attributes to attach to the participant */\n  participantAttributes?: { [key: string]: string };\n  /** Optionally send following DTMF digits (extension codes) when making a call.\n   * Character 'w' can be used to add a 0.5 sec delay. */\n  dtmf?: string;\n  /** @deprecated use `playDialtone` instead */\n  playRingtone?: boolean;\n  /** If `true`, the SIP Participant plays a dial tone to the room until the phone is picked up. */\n  playDialtone?: boolean;\n  /** These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint. */\n  headers?: { [key: string]: string };\n  /** Map SIP response headers from INVITE to sip.h.* participant attributes automatically. */\n  includeHeaders?: SIPHeaderOptions;\n  hidePhoneNumber?: boolean;\n  /** Maximum time for the call to ring in seconds. */\n  ringingTimeout?: number;\n  /** Maximum call duration in seconds. */\n  maxCallDuration?: number;\n  /** If `true`, Krisp noise cancellation will be enabled for the caller. */\n  krispEnabled?: boolean;\n  /** If `true`, this will wait until the call is answered before returning. */\n  waitUntilAnswered?: boolean;\n  /** Optional request timeout in seconds. default 60 seconds if waitUntilAnswered is true, otherwise 10 seconds */\n  timeout?: number;\n}\n\nexport interface ListSipDispatchRuleOptions {\n  /** Pagination options. */\n  page?: Pagination;\n  /** Rule IDs to list. If this option is set, the response will contains rules in the same order. If any of the rules is missing, a nil item in that position will be sent in the response. */\n  dispatchRuleIds?: string[];\n  /** Only list rules that contain one of the Trunk IDs, including wildcard rules. */\n  trunkIds?: string[];\n}\n\nexport interface ListSipTrunkOptions {\n  /** Pagination options. */\n  page?: Pagination;\n  /** Trunk IDs to list. If this option is set, the response will contains trunks in the same order. If any of the trunks is missing, a nil item in that position will be sent in the response. */\n  trunkIds?: string[];\n  /** Only list trunks that contain one of the numbers, including wildcard trunks. */\n  numbers?: string[];\n}\n\nexport interface SipDispatchRuleUpdateOptions {\n  trunkIds?: ListUpdate;\n  rule?: SIPDispatchRule;\n  name?: string;\n  metadata?: string;\n  attributes?: { [key: string]: string };\n}\n\nexport interface SipInboundTrunkUpdateOptions {\n  numbers?: ListUpdate;\n  allowedAddresses?: ListUpdate;\n  allowedNumbers?: ListUpdate;\n  authUsername?: string;\n  authPassword?: string;\n  name?: string;\n  metadata?: string;\n}\n\nexport interface SipOutboundTrunkUpdateOptions {\n  numbers?: ListUpdate;\n  allowedAddresses?: ListUpdate;\n  allowedNumbers?: ListUpdate;\n  authUsername?: string;\n  authPassword?: string;\n  destinationCountry?: string;\n  name?: string;\n  metadata?: string;\n}\n\nexport interface TransferSipParticipantOptions {\n  playDialtone?: boolean;\n  headers?: { [key: string]: string };\n}\n\n/**\n * Client to access Egress APIs\n */\nexport class SipClient extends ServiceBase {\n  private readonly rpc: Rpc;\n\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host: string, apiKey?: string, secret?: string) {\n    super(apiKey, secret);\n    this.rpc = new TwirpRpc(host, livekitPackage);\n  }\n\n  /**\n   * @param number - phone number of the trunk\n   * @param opts - CreateSipTrunkOptions\n   * @deprecated use `createSipInboundTrunk` or `createSipOutboundTrunk`\n   */\n  async createSipTrunk(number: string, opts?: CreateSipTrunkOptions): Promise<SIPTrunkInfo> {\n    let inboundAddresses: string[] | undefined;\n    let inboundNumbers: string[] | undefined;\n    let inboundUsername: string = '';\n    let inboundPassword: string = '';\n    let outboundAddress: string = '';\n    let outboundUsername: string = '';\n    let outboundPassword: string = '';\n    let name: string = '';\n    let metadata: string = '';\n\n    if (opts !== undefined) {\n      inboundAddresses = opts.inbound_addresses;\n      inboundNumbers = opts.inbound_numbers;\n      inboundUsername = opts.inbound_username || '';\n      inboundPassword = opts.inbound_password || '';\n      outboundAddress = opts.outbound_address || '';\n      outboundUsername = opts.outbound_username || '';\n      outboundPassword = opts.outbound_password || '';\n      name = opts.name || '';\n      metadata = opts.metadata || '';\n    }\n\n    const req = new CreateSIPTrunkRequest({\n      name: name,\n      metadata: metadata,\n      inboundAddresses: inboundAddresses,\n      inboundNumbers: inboundNumbers,\n      inboundUsername: inboundUsername,\n      inboundPassword: inboundPassword,\n      outboundNumber: number,\n      outboundAddress: outboundAddress,\n      outboundUsername: outboundUsername,\n      outboundPassword: outboundPassword,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateSIPTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Create a new SIP inbound trunk.\n   *\n   * @param name - human-readable name of the trunk\n   * @param numbers - phone numbers of the trunk\n   * @param opts - CreateSipTrunkOptions\n   * @returns Created SIP inbound trunk\n   */\n  async createSipInboundTrunk(\n    name: string,\n    numbers: string[],\n    opts?: CreateSipInboundTrunkOptions,\n  ): Promise<SIPInboundTrunkInfo> {\n    if (opts === undefined) {\n      opts = {};\n    }\n    const req = new CreateSIPInboundTrunkRequest({\n      trunk: new SIPInboundTrunkInfo({\n        name: name,\n        numbers: numbers,\n        metadata: opts?.metadata,\n        allowedAddresses: opts.allowedAddresses ?? opts.allowed_addresses,\n        allowedNumbers: opts.allowedNumbers ?? opts.allowed_numbers,\n        authUsername: opts.authUsername ?? opts.auth_username,\n        authPassword: opts.authPassword ?? opts.auth_password,\n        headers: opts.headers,\n        headersToAttributes: opts.headersToAttributes,\n        includeHeaders: opts.includeHeaders,\n        krispEnabled: opts.krispEnabled,\n      }),\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateSIPInboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPInboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Create a new SIP outbound trunk.\n   *\n   * @param name - human-readable name of the trunk\n   * @param address - hostname and port of the SIP server to dial\n   * @param numbers - phone numbers of the trunk\n   * @param opts - CreateSipTrunkOptions\n   * @returns Created SIP outbound trunk\n   */\n  async createSipOutboundTrunk(\n    name: string,\n    address: string,\n    numbers: string[],\n    opts?: CreateSipOutboundTrunkOptions,\n  ): Promise<SIPOutboundTrunkInfo> {\n    if (opts === undefined) {\n      opts = {\n        transport: SIPTransport.SIP_TRANSPORT_AUTO,\n      };\n    }\n\n    const req = new CreateSIPOutboundTrunkRequest({\n      trunk: new SIPOutboundTrunkInfo({\n        name: name,\n        address: address,\n        numbers: numbers,\n        metadata: opts.metadata,\n        transport: opts.transport,\n        authUsername: opts.authUsername ?? opts.auth_username,\n        authPassword: opts.authPassword ?? opts.auth_password,\n        headers: opts.headers,\n        headersToAttributes: opts.headersToAttributes,\n        includeHeaders: opts.includeHeaders,\n        destinationCountry: opts.destinationCountry,\n      }),\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateSIPOutboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPOutboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @deprecated use `listSipInboundTrunk` or `listSipOutboundTrunk`\n   */\n  async listSipTrunk(): Promise<Array<SIPTrunkInfo>> {\n    const req: Partial<ListSIPTrunkRequest> = {};\n    const data = await this.rpc.request(\n      svc,\n      'ListSIPTrunk',\n      new ListSIPTrunkRequest(req).toJson(),\n      await this.authHeader({}, { admin: true }),\n    );\n    return ListSIPTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * List SIP inbound trunks with optional filtering.\n   *\n   * @param list - Request with optional filtering parameters\n   * @returns Response containing list of SIP inbound trunks\n   */\n  async listSipInboundTrunk(list: ListSipTrunkOptions = {}): Promise<Array<SIPInboundTrunkInfo>> {\n    const req = new ListSIPInboundTrunkRequest(list).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'ListSIPInboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return ListSIPInboundTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * List SIP outbound trunks with optional filtering.\n   *\n   * @param list - Request with optional filtering parameters\n   * @returns Response containing list of SIP outbound trunks\n   */\n  async listSipOutboundTrunk(list: ListSipTrunkOptions = {}): Promise<Array<SIPOutboundTrunkInfo>> {\n    const req = new ListSIPOutboundTrunkRequest(list).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'ListSIPOutboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return ListSIPOutboundTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * Delete a SIP trunk.\n   *\n   * @param sipTrunkId - ID of the SIP trunk to delete\n   * @returns Deleted trunk information\n   */\n  async deleteSipTrunk(sipTrunkId: string): Promise<SIPTrunkInfo> {\n    const data = await this.rpc.request(\n      svc,\n      'DeleteSIPTrunk',\n      new DeleteSIPTrunkRequest({ sipTrunkId }).toJson(),\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Create a new SIP dispatch rule.\n   *\n   * @param rule - SIP dispatch rule to create\n   * @param opts - CreateSipDispatchRuleOptions\n   * @returns Created SIP dispatch rule\n   */\n  async createSipDispatchRule(\n    rule: SipDispatchRuleDirect | SipDispatchRuleIndividual,\n    opts?: CreateSipDispatchRuleOptions,\n  ): Promise<SIPDispatchRuleInfo> {\n    if (opts === undefined) {\n      opts = {};\n    }\n    let ruleProto: SIPDispatchRule | undefined = undefined;\n    if (rule.type == 'direct') {\n      ruleProto = new SIPDispatchRule({\n        rule: {\n          case: 'dispatchRuleDirect',\n          value: new SIPDispatchRuleDirect({\n            roomName: rule.roomName,\n            pin: rule.pin || '',\n          }),\n        },\n      });\n    } else if (rule.type == 'individual') {\n      ruleProto = new SIPDispatchRule({\n        rule: {\n          case: 'dispatchRuleIndividual',\n          value: new SIPDispatchRuleIndividual({\n            roomPrefix: rule.roomPrefix,\n            pin: rule.pin || '',\n          }),\n        },\n      });\n    }\n\n    const req = new CreateSIPDispatchRuleRequest({\n      rule: ruleProto,\n      trunkIds: opts.trunkIds,\n      hidePhoneNumber: opts.hidePhoneNumber,\n      name: opts.name,\n      metadata: opts.metadata,\n      attributes: opts.attributes,\n      roomPreset: opts.roomPreset,\n      roomConfig: opts.roomConfig,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateSIPDispatchRule',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates an existing SIP dispatch rule by replacing it entirely.\n   *\n   * @param sipDispatchRuleId - ID of the SIP dispatch rule to update\n   * @param rule - new SIP dispatch rule\n   * @returns Updated SIP dispatch rule\n   */\n  async updateSipDispatchRule(\n    sipDispatchRuleId: string,\n    rule: SIPDispatchRuleInfo,\n  ): Promise<SIPDispatchRuleInfo> {\n    const req = new UpdateSIPDispatchRuleRequest({\n      sipDispatchRuleId: sipDispatchRuleId,\n      action: {\n        case: 'replace',\n        value: rule,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPDispatchRule',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates specific fields of an existing SIP dispatch rule.\n   * Only provided fields will be updated.\n   *\n   * @param sipDispatchRuleId - ID of the SIP dispatch rule to update\n   * @param fields - Fields of the dispatch rule to update\n   * @returns Updated SIP dispatch rule\n   */\n  async updateSipDispatchRuleFields(\n    sipDispatchRuleId: string,\n    fields: SipDispatchRuleUpdateOptions = {},\n  ): Promise<SIPDispatchRuleInfo> {\n    const req = new UpdateSIPDispatchRuleRequest({\n      sipDispatchRuleId: sipDispatchRuleId,\n      action: {\n        case: 'update',\n        value: fields,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPDispatchRule',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates an existing SIP inbound trunk by replacing it entirely.\n   *\n   * @param sipTrunkId - ID of the SIP inbound trunk to update\n   * @param trunk - SIP inbound trunk to update with\n   * @returns Updated SIP inbound trunk\n   */\n  async updateSipInboundTrunk(\n    sipTrunkId: string,\n    trunk: SIPInboundTrunkInfo,\n  ): Promise<SIPInboundTrunkInfo> {\n    const req = new UpdateSIPInboundTrunkRequest({\n      sipTrunkId,\n      action: {\n        case: 'replace',\n        value: trunk,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPInboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPInboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates specific fields of an existing SIP inbound trunk.\n   * Only provided fields will be updated.\n   *\n   * @param sipTrunkId - ID of the SIP inbound trunk to update\n   * @param fields - Fields of the inbound trunk to update\n   * @returns Updated SIP inbound trunk\n   */\n  async updateSipInboundTrunkFields(\n    sipTrunkId: string,\n    fields: SipInboundTrunkUpdateOptions,\n  ): Promise<SIPInboundTrunkInfo> {\n    const req = new UpdateSIPInboundTrunkRequest({\n      sipTrunkId,\n      action: {\n        case: 'update',\n        value: fields,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPInboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPInboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates an existing SIP outbound trunk by replacing it entirely.\n   *\n   * @param sipTrunkId - ID of the SIP outbound trunk to update\n   * @param trunk - SIP outbound trunk to update with\n   * @returns Updated SIP outbound trunk\n   */\n  async updateSipOutboundTrunk(\n    sipTrunkId: string,\n    trunk: SIPOutboundTrunkInfo,\n  ): Promise<SIPOutboundTrunkInfo> {\n    const req = new UpdateSIPOutboundTrunkRequest({\n      sipTrunkId,\n      action: {\n        case: 'replace',\n        value: trunk,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPOutboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPOutboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Updates specific fields of an existing SIP outbound trunk.\n   * Only provided fields will be updated.\n   *\n   * @param sipTrunkId - ID of the SIP outbound trunk to update\n   * @param fields - Fields of the outbound trunk to update\n   * @returns Updated SIP outbound trunk\n   */\n  async updateSipOutboundTrunkFields(\n    sipTrunkId: string,\n    fields: SipOutboundTrunkUpdateOptions,\n  ): Promise<SIPOutboundTrunkInfo> {\n    const req = new UpdateSIPOutboundTrunkRequest({\n      sipTrunkId,\n      action: {\n        case: 'update',\n        value: fields,\n      },\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateSIPOutboundTrunk',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n\n    return SIPOutboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * List SIP dispatch rules with optional filtering.\n   *\n   * @param list - Request with optional filtering parameters\n   * @returns Response containing list of SIP dispatch rules\n   */\n  async listSipDispatchRule(\n    list: ListSipDispatchRuleOptions = {},\n  ): Promise<Array<SIPDispatchRuleInfo>> {\n    const req = new ListSIPDispatchRuleRequest(list).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'ListSIPDispatchRule',\n      req,\n      await this.authHeader({}, { admin: true }),\n    );\n    return ListSIPDispatchRuleResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * Delete a SIP dispatch rule.\n   *\n   * @param sipDispatchRuleId - ID of the SIP dispatch rule to delete\n   * @returns Deleted rule information\n   */\n  async deleteSipDispatchRule(sipDispatchRuleId: string): Promise<SIPDispatchRuleInfo> {\n    const data = await this.rpc.request(\n      svc,\n      'DeleteSIPDispatchRule',\n      new DeleteSIPDispatchRuleRequest({ sipDispatchRuleId }).toJson(),\n      await this.authHeader({}, { admin: true }),\n    );\n    return SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Create a new SIP participant.\n   *\n   * @param sipTrunkId - sip trunk to use for the call\n   * @param number - number to dial\n   * @param roomName - room to attach the call to\n   * @param opts - CreateSipParticipantOptions\n   * @returns Created SIP participant\n   */\n  async createSipParticipant(\n    sipTrunkId: string,\n    number: string,\n    roomName: string,\n    opts?: CreateSipParticipantOptions,\n  ): Promise<SIPParticipantInfo> {\n    if (opts === undefined) {\n      opts = {};\n    }\n\n    if (opts.timeout === undefined) {\n      opts.timeout = opts.waitUntilAnswered ? 60 : 10;\n    }\n\n    const req = new CreateSIPParticipantRequest({\n      sipTrunkId: sipTrunkId,\n      sipCallTo: number,\n      sipNumber: opts.fromNumber,\n      roomName: roomName,\n      participantIdentity: opts.participantIdentity || 'sip-participant',\n      participantName: opts.participantName,\n      participantMetadata: opts.participantMetadata,\n      participantAttributes: opts.participantAttributes,\n      dtmf: opts.dtmf,\n      playDialtone: opts.playDialtone ?? opts.playRingtone,\n      headers: opts.headers,\n      hidePhoneNumber: opts.hidePhoneNumber,\n      includeHeaders: opts.includeHeaders,\n      ringingTimeout: opts.ringingTimeout\n        ? new Duration({ seconds: BigInt(opts.ringingTimeout) })\n        : undefined,\n      maxCallDuration: opts.maxCallDuration\n        ? new Duration({ seconds: BigInt(opts.maxCallDuration) })\n        : undefined,\n      krispEnabled: opts.krispEnabled,\n      waitUntilAnswered: opts.waitUntilAnswered,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'CreateSIPParticipant',\n      req,\n      await this.authHeader({}, { call: true }),\n      opts.timeout,\n    );\n    return SIPParticipantInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Transfer a SIP participant to a different room.\n   *\n   * @param roomName - room the SIP participant to transfer is connectd to\n   * @param participantIdentity - identity of the SIP participant to transfer\n   * @param transferTo - SIP URL to transfer the participant to\n   * @param opts - TransferSipParticipantOptions\n   */\n  async transferSipParticipant(\n    roomName: string,\n    participantIdentity: string,\n    transferTo: string,\n    opts?: TransferSipParticipantOptions,\n  ): Promise<void> {\n    if (opts === undefined) {\n      opts = {};\n    }\n\n    const req = new TransferSIPParticipantRequest({\n      participantIdentity: participantIdentity,\n      roomName: roomName,\n      transferTo: transferTo,\n      playDialtone: opts.playDialtone,\n      headers: opts.headers,\n    }).toJson();\n\n    await this.rpc.request(\n      svc,\n      'TransferSIPParticipant',\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }, { call: true }),\n    );\n  }\n}\n"], "mappings": "AAGA,SAAS,gBAAgB;AAOzB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,mBAAmB;AAE5B,SAAS,UAAU,sBAAsB;AAEzC,MAAM,MAAM;AAoKL,MAAM,kBAAkB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzC,YAAY,MAAc,QAAiB,QAAiB;AAC1D,UAAM,QAAQ,MAAM;AACpB,SAAK,MAAM,IAAI,SAAS,MAAM,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,QAAgB,MAAqD;AACxF,QAAI;AACJ,QAAI;AACJ,QAAI,kBAA0B;AAC9B,QAAI,kBAA0B;AAC9B,QAAI,kBAA0B;AAC9B,QAAI,mBAA2B;AAC/B,QAAI,mBAA2B;AAC/B,QAAI,OAAe;AACnB,QAAI,WAAmB;AAEvB,QAAI,SAAS,QAAW;AACtB,yBAAmB,KAAK;AACxB,uBAAiB,KAAK;AACtB,wBAAkB,KAAK,oBAAoB;AAC3C,wBAAkB,KAAK,oBAAoB;AAC3C,wBAAkB,KAAK,oBAAoB;AAC3C,yBAAmB,KAAK,qBAAqB;AAC7C,yBAAmB,KAAK,qBAAqB;AAC7C,aAAO,KAAK,QAAQ;AACpB,iBAAW,KAAK,YAAY;AAAA,IAC9B;AAEA,UAAM,MAAM,IAAI,sBAAsB;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,aAAa,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,sBACJ,MACA,SACA,MAC8B;AAC9B,QAAI,SAAS,QAAW;AACtB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C,OAAO,IAAI,oBAAoB;AAAA,QAC7B;AAAA,QACA;AAAA,QACA,UAAU,6BAAM;AAAA,QAChB,kBAAkB,KAAK,oBAAoB,KAAK;AAAA,QAChD,gBAAgB,KAAK,kBAAkB,KAAK;AAAA,QAC5C,cAAc,KAAK,gBAAgB,KAAK;AAAA,QACxC,cAAc,KAAK,gBAAgB,KAAK;AAAA,QACxC,SAAS,KAAK;AAAA,QACd,qBAAqB,KAAK;AAAA,QAC1B,gBAAgB,KAAK;AAAA,QACrB,cAAc,KAAK;AAAA,MACrB,CAAC;AAAA,IACH,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,uBACJ,MACA,SACA,SACA,MAC+B;AAC/B,QAAI,SAAS,QAAW;AACtB,aAAO;AAAA,QACL,WAAW,aAAa;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,MAAM,IAAI,8BAA8B;AAAA,MAC5C,OAAO,IAAI,qBAAqB;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB,cAAc,KAAK,gBAAgB,KAAK;AAAA,QACxC,cAAc,KAAK,gBAAgB,KAAK;AAAA,QACxC,SAAS,KAAK;AAAA,QACd,qBAAqB,KAAK;AAAA,QAC1B,gBAAgB,KAAK;AAAA,QACrB,oBAAoB,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,qBAAqB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAA6C;AACjD,UAAM,MAAoC,CAAC;AAC3C,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,oBAAoB,GAAG,EAAE,OAAO;AAAA,MACpC,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,qBAAqB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,oBAAoB,OAA4B,CAAC,GAAwC;AAC7F,UAAM,MAAM,IAAI,2BAA2B,IAAI,EAAE,OAAO;AACxD,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,4BAA4B,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,qBAAqB,OAA4B,CAAC,GAAyC;AAC/F,UAAM,MAAM,IAAI,4BAA4B,IAAI,EAAE,OAAO;AACzD,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,6BAA6B,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,eAAe,YAA2C;AAC9D,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,sBAAsB,EAAE,WAAW,CAAC,EAAE,OAAO;AAAA,MACjD,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,aAAa,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,sBACJ,MACA,MAC8B;AAC9B,QAAI,SAAS,QAAW;AACtB,aAAO,CAAC;AAAA,IACV;AACA,QAAI,YAAyC;AAC7C,QAAI,KAAK,QAAQ,UAAU;AACzB,kBAAY,IAAI,gBAAgB;AAAA,QAC9B,MAAM;AAAA,UACJ,MAAM;AAAA,UACN,OAAO,IAAI,sBAAsB;AAAA,YAC/B,UAAU,KAAK;AAAA,YACf,KAAK,KAAK,OAAO;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,WAAW,KAAK,QAAQ,cAAc;AACpC,kBAAY,IAAI,gBAAgB;AAAA,QAC9B,MAAM;AAAA,UACJ,MAAM;AAAA,UACN,OAAO,IAAI,0BAA0B;AAAA,YACnC,YAAY,KAAK;AAAA,YACjB,KAAK,KAAK,OAAO;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C,MAAM;AAAA,MACN,UAAU,KAAK;AAAA,MACf,iBAAiB,KAAK;AAAA,MACtB,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,IACnB,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,sBACJ,mBACA,MAC8B;AAC9B,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,4BACJ,mBACA,SAAuC,CAAC,GACV;AAC9B,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,sBACJ,YACA,OAC8B;AAC9B,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,4BACJ,YACA,QAC8B;AAC9B,UAAM,MAAM,IAAI,6BAA6B;AAAA,MAC3C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,uBACJ,YACA,OAC+B;AAC/B,UAAM,MAAM,IAAI,8BAA8B;AAAA,MAC5C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,qBAAqB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,6BACJ,YACA,QAC+B;AAC/B,UAAM,MAAM,IAAI,8BAA8B;AAAA,MAC5C;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AAEA,WAAO,qBAAqB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,oBACJ,OAAmC,CAAC,GACC;AACrC,UAAM,MAAM,IAAI,2BAA2B,IAAI,EAAE,OAAO;AACxD,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,4BAA4B,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,sBAAsB,mBAAyD;AACnF,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,OAAO;AAAA,MAC/D,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,WAAO,oBAAoB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,qBACJ,YACA,QACA,UACA,MAC6B;AAC7B,QAAI,SAAS,QAAW;AACtB,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,KAAK,YAAY,QAAW;AAC9B,WAAK,UAAU,KAAK,oBAAoB,KAAK;AAAA,IAC/C;AAEA,UAAM,MAAM,IAAI,4BAA4B;AAAA,MAC1C;AAAA,MACA,WAAW;AAAA,MACX,WAAW,KAAK;AAAA,MAChB;AAAA,MACA,qBAAqB,KAAK,uBAAuB;AAAA,MACjD,iBAAiB,KAAK;AAAA,MACtB,qBAAqB,KAAK;AAAA,MAC1B,uBAAuB,KAAK;AAAA,MAC5B,MAAM,KAAK;AAAA,MACX,cAAc,KAAK,gBAAgB,KAAK;AAAA,MACxC,SAAS,KAAK;AAAA,MACd,iBAAiB,KAAK;AAAA,MACtB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK,iBACjB,IAAI,SAAS,EAAE,SAAS,OAAO,KAAK,cAAc,EAAE,CAAC,IACrD;AAAA,MACJ,iBAAiB,KAAK,kBAClB,IAAI,SAAS,EAAE,SAAS,OAAO,KAAK,eAAe,EAAE,CAAC,IACtD;AAAA,MACJ,cAAc,KAAK;AAAA,MACnB,mBAAmB,KAAK;AAAA,IAC1B,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,CAAC,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,MACxC,KAAK;AAAA,IACP;AACA,WAAO,mBAAmB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,uBACJ,UACA,qBACA,YACA,MACe;AACf,QAAI,SAAS,QAAW;AACtB,aAAO,CAAC;AAAA,IACV;AAEA,UAAM,MAAM,IAAI,8BAA8B;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,KAAK;AAAA,MACnB,SAAS,KAAK;AAAA,IAChB,CAAC,EAAE,OAAO;AAEV,UAAM,KAAK,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,SAAS,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,IAC3E;AAAA,EACF;AACF;", "names": []}