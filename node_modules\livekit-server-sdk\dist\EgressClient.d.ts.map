{"version": 3, "file": "EgressClient.d.ts", "sourceRoot": "", "sources": ["../src/EgressClient.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,qBAAqB,EACrB,WAAW,EACX,mBAAmB,EACnB,YAAY,EACb,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,WAAW,EACX,UAAU,EAWX,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAM/C,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,eAAe,CAAC,EAAE,qBAAqB,GAAG,eAAe,CAAC;IAC1D;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;CAC3B;AAED,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,eAAe,CAAC,EAAE,qBAAqB,GAAG,eAAe,CAAC;IAC1D;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,wBAAwB;IACvC;;;OAGG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,eAAe,CAAC,EAAE,qBAAqB,GAAG,eAAe,CAAC;CAC3D;AAED,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,eAAe,CAAC,EAAE,qBAAqB,GAAG,eAAe,CAAC;CAC3D;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,IAAI,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;IACrC,MAAM,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IAClC,QAAQ,CAAC,EAAE,mBAAmB,GAAG,SAAS,CAAC;IAC3C,MAAM,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;CAClC;AAED,MAAM,WAAW,iBAAiB;IAChC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED;;GAEG;AACH,qBAAa,YAAa,SAAQ,WAAW;IAC3C,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAM;IAE1B;;;;OAIG;gBACS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;IAK1D;;;;OAIG;IACG,wBAAwB,CAC5B,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,cAAc,GAAG,iBAAiB,GAAG,YAAY,GAAG,mBAAmB,EAC/E,IAAI,CAAC,EAAE,oBAAoB,GAC1B,OAAO,CAAC,UAAU,CAAC;IACtB;;OAEG;IACG,wBAAwB,CAC5B,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,cAAc,GAAG,iBAAiB,GAAG,YAAY,GAAG,mBAAmB,EAC/E,MAAM,CAAC,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,qBAAqB,GAAG,eAAe,EACjD,SAAS,CAAC,EAAE,OAAO,EACnB,SAAS,CAAC,EAAE,OAAO,EACnB,aAAa,CAAC,EAAE,MAAM,EACtB,WAAW,CAAC,EAAE,WAAW,GACxB,OAAO,CAAC,UAAU,CAAC;IAiEtB;;;;OAIG;IACG,cAAc,CAClB,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,cAAc,GAAG,iBAAiB,GAAG,YAAY,GAAG,mBAAmB,EAC/E,IAAI,CAAC,EAAE,UAAU,GAChB,OAAO,CAAC,UAAU,CAAC;IAmCtB;;;;;;OAMG;IACG,sBAAsB,CAC1B,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,cAAc,EACtB,IAAI,CAAC,EAAE,wBAAwB,GAC9B,OAAO,CAAC,UAAU,CAAC;IAuBtB;;;;OAIG;IACG,yBAAyB,CAC7B,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,cAAc,GAAG,iBAAiB,GAAG,YAAY,GAAG,mBAAmB,EAC/E,IAAI,CAAC,EAAE,qBAAqB,GAC3B,OAAO,CAAC,UAAU,CAAC;IACtB;;OAEG;IACG,yBAAyB,CAC7B,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,cAAc,GAAG,iBAAiB,GAAG,YAAY,GAAG,mBAAmB,EAC/E,YAAY,CAAC,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,MAAM,EACrB,OAAO,CAAC,EAAE,qBAAqB,GAAG,eAAe,GAChD,OAAO,CAAC,UAAU,CAAC;IAqDtB,OAAO,CAAC,gBAAgB;IAUxB,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,qBAAqB;IAS7B,OAAO,CAAC,cAAc;IAMtB,OAAO,CAAC,eAAe;IAqGvB;;;;OAIG;IACG,gBAAgB,CACpB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GAAG,MAAM,EACjC,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,UAAU,CAAC;IAuCtB;;;OAGG;IACG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IAUzE;;;;OAIG;IACG,YAAY,CAChB,QAAQ,EAAE,MAAM,EAChB,aAAa,CAAC,EAAE,MAAM,EAAE,EACxB,gBAAgB,CAAC,EAAE,MAAM,EAAE,GAC1B,OAAO,CAAC,UAAU,CAAC;IAatB;;;OAGG;IACG,UAAU,CAAC,OAAO,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACzE;;;OAGG;IACG,UAAU,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAqB/D;;OAEG;IACG,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;CASxD"}