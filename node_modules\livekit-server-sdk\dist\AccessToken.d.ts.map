{"version": 3, "file": "AccessToken.d.ts", "sourceRoot": "", "sources": ["../src/AccessToken.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAE3D,OAAO,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAQrE,MAAM,WAAW,kBAAkB;IACjC;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAEtB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACrC;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,MAAM,CAAS;IAEvB,OAAO,CAAC,SAAS,CAAS;IAE1B,OAAO,CAAC,MAAM,CAAc;IAE5B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;IAErB;;;;OAIG;gBACS,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,kBAAkB;IAsC7E;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,UAAU;IAI1B;;;OAGG;IACH,WAAW,CAAC,KAAK,EAAE,QAAQ;IAI3B,IAAI,IAAI,IAAI,MAAM,GAAG,SAAS,CAE7B;IAED,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAEpB;IAED,IAAI,QAAQ,IAAI,MAAM,GAAG,SAAS,CAEjC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,EAAE,EAAE,MAAM,EAEtB;IAED,IAAI,UAAU,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS,CAEnD;IAED,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAE3C;IAED,IAAI,IAAI,IAAI,MAAM,GAAG,SAAS,CAE7B;IAED,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAEpB;IAED,IAAI,MAAM,IAAI,MAAM,GAAG,SAAS,CAE/B;IAED,IAAI,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,EAEjC;IAED,IAAI,UAAU,IAAI,MAAM,GAAG,SAAS,CAEnC;IAED,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,EAExC;IAED,IAAI,UAAU,IAAI,iBAAiB,GAAG,SAAS,CAE9C;IAED,IAAI,UAAU,CAAC,MAAM,EAAE,iBAAiB,GAAG,SAAS,EAEnD;IAED;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;CAiB/B;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAAS;IAEvB,OAAO,CAAC,SAAS,CAAS;gBAEd,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAKvC,MAAM,CACV,KAAK,EAAE,MAAM,EACb,cAAc,GAAE,MAAM,GAAG,MAAqC,GAC7D,OAAO,CAAC,WAAW,CAAC;CAYxB"}