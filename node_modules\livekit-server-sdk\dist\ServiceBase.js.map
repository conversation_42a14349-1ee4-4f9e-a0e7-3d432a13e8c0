{"version": 3, "sources": ["../src/ServiceBase.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport { AccessToken } from './AccessToken.js';\nimport type { SIPGrant, VideoGrant } from './grants.js';\n\n/**\n * Utilities to handle authentication\n */\nexport class ServiceBase {\n  private readonly apiKey?: string;\n\n  private readonly secret?: string;\n\n  private readonly ttl: string;\n\n  /**\n   * @param apiKey - API Key.\n   * @param secret - API Secret.\n   * @param ttl - token TTL\n   */\n  constructor(apiKey?: string, secret?: string, ttl?: string) {\n    this.apiKey = apiKey;\n    this.secret = secret;\n    this.ttl = ttl || '10m';\n  }\n\n  async authHeader(grant: VideoGrant, sip?: SIPGrant): Promise<Record<string, string>> {\n    const at = new AccessToken(this.apiKey, this.secret, { ttl: this.ttl });\n    if (grant) {\n      at.addGrant(grant);\n    }\n    if (sip) {\n      at.addSIPGrant(sip);\n    }\n    return {\n      Authorization: `Bearer ${await at.toJwt()}`,\n    };\n  }\n}\n"], "mappings": "AAGA,SAAS,mBAAmB;AAMrB,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYvB,YAAY,QAAiB,QAAiB,KAAc;AAC1D,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,MAAM,OAAO;AAAA,EACpB;AAAA,EAEA,MAAM,WAAW,OAAmB,KAAiD;AACnF,UAAM,KAAK,IAAI,YAAY,KAAK,QAAQ,KAAK,QAAQ,EAAE,KAAK,KAAK,IAAI,CAAC;AACtE,QAAI,OAAO;AACT,SAAG,SAAS,KAAK;AAAA,IACnB;AACA,QAAI,KAAK;AACP,SAAG,YAAY,GAAG;AAAA,IACpB;AACA,WAAO;AAAA,MACL,eAAe,UAAU,MAAM,GAAG,MAAM,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;", "names": []}