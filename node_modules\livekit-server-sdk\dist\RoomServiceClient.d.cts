import { RoomEgress, ParticipantPermission, Room, ParticipantInfo, TrackInfo, DataPacket_Kind } from '@livekit/protocol';
import { ServiceBase } from './ServiceBase.cjs';
import './grants.cjs';
import 'jose';

/**
 * Options for when creating a room
 */
interface CreateOptions {
    /**
     * name of the room. required
     */
    name: string;
    /**
     * number of seconds to keep the room open before any participant joins
     */
    emptyTimeout?: number;
    /**
     * number of seconds to keep the room open after the last participant leaves
     * this option is helpful to give a grace period for participants to re-join
     */
    departureTimeout?: number;
    /**
     * limit to the number of participants in a room at a time
     */
    maxParticipants?: number;
    /**
     * initial room metadata
     */
    metadata?: string;
    /**
     * add egress options
     */
    egress?: RoomEgress;
    /**
     * minimum playout delay in milliseconds
     */
    minPlayoutDelay?: number;
    /**
     * maximum playout delay in milliseconds
     */
    maxPlayoutDelay?: number;
    /**
     * improves A/V sync when min_playout_delay set to a value larger than 200ms.
     * It will disables transceiver re-use -- this option is not recommended
     * for rooms with frequent subscription changes
     */
    syncStreams?: boolean;
    /**
     * override the node room is allocated to, for debugging
     * does not work with Cloud
     */
    nodeId?: string;
}
type SendDataOptions = {
    /** If set, only deliver to listed participant identities */
    destinationIdentities?: string[];
    destinationSids?: string[];
    topic?: string;
};
type UpdateParticipantOptions = {
    /** only attributes you'd want to update should be set, set value to empty string to remove it */
    attributes?: {
        [key: string]: string;
    };
    metadata?: string;
    /** permissions are updated atomically - all desired permissions would need to be set */
    permission?: Partial<ParticipantPermission>;
    name?: string;
};
/**
 * Client to access Room APIs
 */
declare class RoomServiceClient extends ServiceBase {
    private readonly rpc;
    /**
     *
     * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'
     * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY
     * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET
     */
    constructor(host: string, apiKey?: string, secret?: string);
    /**
     * Creates a new room. Explicit room creation is not required, since rooms will
     * be automatically created when the first participant joins. This method can be
     * used to customize room settings.
     * @param options -
     */
    createRoom(options: CreateOptions): Promise<Room>;
    /**
     * List active rooms
     * @param names - when undefined or empty, list all rooms.
     *                otherwise returns rooms with matching names
     * @returns
     */
    listRooms(names?: string[]): Promise<Room[]>;
    deleteRoom(room: string): Promise<void>;
    /**
     * Update metadata of a room
     * @param room - name of the room
     * @param metadata - the new metadata for the room
     */
    updateRoomMetadata(room: string, metadata: string): Promise<Room>;
    /**
     * List participants in a room
     * @param room - name of the room
     */
    listParticipants(room: string): Promise<ParticipantInfo[]>;
    /**
     * Get information on a specific participant, including the tracks that participant
     * has published
     * @param room - name of the room
     * @param identity - identity of the participant to return
     */
    getParticipant(room: string, identity: string): Promise<ParticipantInfo>;
    /**
     * Removes a participant in the room. This will disconnect the participant
     * and will emit a Disconnected event for that participant.
     * Even after being removed, the participant can still re-join the room.
     * @param room -
     * @param identity -
     */
    removeParticipant(room: string, identity: string): Promise<void>;
    /**
     * Forwards a participant's track to another room. This will create a
     * participant to join the destination room that has same information
     * with the source participant except the kind to be `Forwarded`. All
     * changes to the source participant will be reflected to the forwarded
     * participant. When the source participant disconnects or the
     * `RemoveParticipant` method is called in the destination room, the
     * forwarding will be stopped.
     * @param room -
     * @param identity -
     * @param destinationRoom - the room to forward the participant to
     */
    forwardParticipant(room: string, identity: string, destinationRoom: string): Promise<void>;
    /**
     * Move a connected participant to a different room. Requires `roomAdmin` and `destinationRoom`.
     * The participant will be removed from the current room and added to the destination room.
     * From the other observers' perspective, the participant would've disconnected from the previous room and joined the new one.
     * @param room -
     * @param identity -
     * @param destinationRoom - the room to move the participant to
     */
    moveParticipant(room: string, identity: string, destinationRoom: string): Promise<void>;
    /**
     * Mutes a track that the participant has published.
     * @param room -
     * @param identity -
     * @param trackSid - sid of the track to be muted
     * @param muted - true to mute, false to unmute
     */
    mutePublishedTrack(room: string, identity: string, trackSid: string, muted: boolean): Promise<TrackInfo>;
    /**
     * Updates a participant's state or permissions
     * @param room - target room
     * @param identity - participant identity
     * @param options - participant fields to update
     */
    updateParticipant(room: string, identity: string, options: UpdateParticipantOptions): Promise<ParticipantInfo>;
    /**
     * Updates a participant's state or permissions
     * @param room - target room
     * @param identity - participant identity
     * @param options - participant fields to update
     */
    updateParticipant(room: string, identity: string, metadata?: string, permission?: Partial<ParticipantPermission>, name?: string): Promise<ParticipantInfo>;
    /**
     * Updates a participant's subscription to tracks
     * @param room -
     * @param identity -
     * @param trackSids -
     * @param subscribe - true to subscribe, false to unsubscribe
     */
    updateSubscriptions(room: string, identity: string, trackSids: string[], subscribe: boolean): Promise<void>;
    /**
     * Sends data message to participants in the room
     * @param room -
     * @param data - opaque payload to send
     * @param kind - delivery reliability
     * @param options - optionally specify a topic and destinationSids (when destinationSids is empty, message is sent to everyone)
     */
    sendData(room: string, data: Uint8Array, kind: DataPacket_Kind, options: SendDataOptions): Promise<void>;
    /**
     * Sends data message to participants in the room
     * @deprecated use sendData(room, data, kind, options) instead
     * @param room -
     * @param data - opaque payload to send
     * @param kind - delivery reliability
     * @param destinationSids - optional. when empty, message is sent to everyone
     */
    sendData(room: string, data: Uint8Array, kind: DataPacket_Kind, destinationSids?: string[]): Promise<void>;
}

export { type CreateOptions, RoomServiceClient, type SendDataOptions, type UpdateParticipantOptions };
