{"version": 3, "sources": ["../../src/crypto/digest.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\n\n// Use the Web Crypto API if available, otherwise fallback to Node.js crypto\nexport async function digest(data: string): Promise<ArrayBuffer> {\n  if (globalThis.crypto?.subtle) {\n    const encoder = new TextEncoder();\n    return crypto.subtle.digest('SHA-256', encoder.encode(data));\n  } else {\n    const nodeCrypto = await import('node:crypto');\n    return nodeCrypto.createHash('sha256').update(data).digest();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,eAAsB,OAAO,MAAoC;AALjE;AAME,OAAI,gBAAW,WAAX,mBAAmB,QAAQ;AAC7B,UAAM,UAAU,IAAI,YAAY;AAChC,WAAO,OAAO,OAAO,OAAO,WAAW,QAAQ,OAAO,IAAI,CAAC;AAAA,EAC7D,OAAO;AACL,UAAM,aAAa,MAAM,OAAO,aAAa;AAC7C,WAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,EAC7D;AACF;", "names": []}