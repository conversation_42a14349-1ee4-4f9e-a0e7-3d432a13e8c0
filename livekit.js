import { SipClient } from 'livekit-server-sdk';

// <PERSON><PERSON><PERSON> hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo SIP client
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);

// Cấu hình cuộc gọi
const trunkId = 'ST_pxFYJVBYt5cd';  // ID của outbound trunk
const phoneNumber = '+84938709344';  // Số điện thoại cần gọi
const roomName = 'my-sip-room';      // Tên room LiveKit

// Tùy chọn cho SIP participant
const sipParticipantOptions = {
    sipTrunkId: trunkId,
    sipCallTo: phoneNumber,
    roomName: roomName,
    participantIdentity: 'sip-caller-' + Date.now(),
    participantName: 'Test Caller',
    participantMetadata: 'Outbound call test',
    krispEnabled: true,
    waitUntilAnswered: true,
    playDialtone: false,
    hidePhoneNumber: false,
    ringingTimeout: { seconds: 30 },  // Timeout 30 giây
    maxCallDuration: { seconds: 300 } // Tối đa 5 phút
};

async function main() {
    console.log('=== LiveKit SIP Outbound Call Test ===');
    console.log('LiveKit URL:', livekitUrl);
    console.log('Trunk ID:', trunkId);
    console.log('Phone number:', phoneNumber);
    console.log('Room name:', roomName);
    console.log('');

    try {
        console.log('Creating SIP participant for outbound call...');

        // Sử dụng API đúng theo documentation
        const participant = await sipClient.createSipParticipant(sipParticipantOptions);

        console.log('✅ SIP participant created successfully!');
        console.log('Participant details:');
        console.log('- Participant ID:', participant.participantId);
        console.log('- Participant Identity:', participant.participantIdentity);
        console.log('- Room Name:', participant.roomName);
        console.log('- SIP Call ID:', participant.sipCallId);

        console.log('\n🔔 Outbound call initiated to:', phoneNumber);
        console.log('📞 Call should be connecting...');

    } catch (error) {
        console.error('❌ Error creating SIP participant:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);

        // Kiểm tra các lỗi phổ biến
        if (error.message.includes('timeout')) {
            console.error('\n💡 Troubleshooting tips:');
            console.error('- Check your internet connection');
            console.error('- Verify LiveKit server is accessible');
            console.error('- Check if trunk ID is valid and active');
        } else if (error.message.includes('unauthorized') || error.message.includes('403')) {
            console.error('\n💡 Authentication issue:');
            console.error('- Verify API key and secret are correct');
            console.error('- Check if token has SIP call permissions');
        } else if (error.message.includes('trunk')) {
            console.error('\n💡 Trunk configuration issue:');
            console.error('- Verify trunk ID exists and is configured properly');
            console.error('- Check trunk has outbound calling enabled');
        }

        if (error.stack) {
            console.error('\nStack trace:', error.stack);
        }
    }
}

console.log('🚀 Starting LiveKit SIP outbound call test...');
main().then(() => {
    console.log('\n✅ Script execution completed');
}).catch((error) => {
    console.error('\n❌ Unhandled error in main:', error);
    process.exit(1);
});