"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Mixin = exports.Method = exports.Api = void 0;
const message_js_1 = require("../../message.js");
const type_pb_js_1 = require("./type_pb.js");
const source_context_pb_js_1 = require("./source_context_pb.js");
const proto3_js_1 = require("../../proto3.js");
/**
 * Api is a light-weight descriptor for an API Interface.
 *
 * Interfaces are also described as "protocol buffer services" in some contexts,
 * such as by the "service" keyword in a .proto file, but they are different
 * from API Services, which represent a concrete implementation of an interface
 * as opposed to simply a description of methods and bindings. They are also
 * sometimes simply referred to as "APIs" in other contexts, such as the name of
 * this message itself. See https://cloud.google.com/apis/design/glossary for
 * detailed terminology.
 *
 * @generated from message google.protobuf.Api
 */
class Api extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The fully qualified name of this interface, including package name
         * followed by the interface's simple name.
         *
         * @generated from field: string name = 1;
         */
        this.name = "";
        /**
         * The methods of this interface, in unspecified order.
         *
         * @generated from field: repeated google.protobuf.Method methods = 2;
         */
        this.methods = [];
        /**
         * Any metadata attached to the interface.
         *
         * @generated from field: repeated google.protobuf.Option options = 3;
         */
        this.options = [];
        /**
         * A version string for this interface. If specified, must have the form
         * `major-version.minor-version`, as in `1.10`. If the minor version is
         * omitted, it defaults to zero. If the entire version field is empty, the
         * major version is derived from the package name, as outlined below. If the
         * field is not empty, the version in the package name will be verified to be
         * consistent with what is provided here.
         *
         * The versioning schema uses [semantic
         * versioning](http://semver.org) where the major version number
         * indicates a breaking change and the minor version an additive,
         * non-breaking change. Both version numbers are signals to users
         * what to expect from different versions, and should be carefully
         * chosen based on the product plan.
         *
         * The major version is also reflected in the package name of the
         * interface, which must end in `v<major-version>`, as in
         * `google.feature.v1`. For major versions 0 and 1, the suffix can
         * be omitted. Zero major versions must only be used for
         * experimental, non-GA interfaces.
         *
         *
         * @generated from field: string version = 4;
         */
        this.version = "";
        /**
         * Included interfaces. See [Mixin][].
         *
         * @generated from field: repeated google.protobuf.Mixin mixins = 6;
         */
        this.mixins = [];
        /**
         * The source syntax of the service.
         *
         * @generated from field: google.protobuf.Syntax syntax = 7;
         */
        this.syntax = type_pb_js_1.Syntax.PROTO2;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new Api().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Api().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Api().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Api, a, b);
    }
}
exports.Api = Api;
Api.runtime = proto3_js_1.proto3;
Api.typeName = "google.protobuf.Api";
Api.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "methods", kind: "message", T: Method, repeated: true },
    { no: 3, name: "options", kind: "message", T: type_pb_js_1.Option, repeated: true },
    { no: 4, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "source_context", kind: "message", T: source_context_pb_js_1.SourceContext },
    { no: 6, name: "mixins", kind: "message", T: Mixin, repeated: true },
    { no: 7, name: "syntax", kind: "enum", T: proto3_js_1.proto3.getEnumType(type_pb_js_1.Syntax) },
]);
/**
 * Method represents a method of an API interface.
 *
 * @generated from message google.protobuf.Method
 */
class Method extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The simple name of this method.
         *
         * @generated from field: string name = 1;
         */
        this.name = "";
        /**
         * A URL of the input message type.
         *
         * @generated from field: string request_type_url = 2;
         */
        this.requestTypeUrl = "";
        /**
         * If true, the request is streamed.
         *
         * @generated from field: bool request_streaming = 3;
         */
        this.requestStreaming = false;
        /**
         * The URL of the output message type.
         *
         * @generated from field: string response_type_url = 4;
         */
        this.responseTypeUrl = "";
        /**
         * If true, the response is streamed.
         *
         * @generated from field: bool response_streaming = 5;
         */
        this.responseStreaming = false;
        /**
         * Any metadata attached to the method.
         *
         * @generated from field: repeated google.protobuf.Option options = 6;
         */
        this.options = [];
        /**
         * The source syntax of this method.
         *
         * @generated from field: google.protobuf.Syntax syntax = 7;
         */
        this.syntax = type_pb_js_1.Syntax.PROTO2;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new Method().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Method().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Method().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Method, a, b);
    }
}
exports.Method = Method;
Method.runtime = proto3_js_1.proto3;
Method.typeName = "google.protobuf.Method";
Method.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "request_type_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "request_streaming", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "response_type_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "response_streaming", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "options", kind: "message", T: type_pb_js_1.Option, repeated: true },
    { no: 7, name: "syntax", kind: "enum", T: proto3_js_1.proto3.getEnumType(type_pb_js_1.Syntax) },
]);
/**
 * Declares an API Interface to be included in this interface. The including
 * interface must redeclare all the methods from the included interface, but
 * documentation and options are inherited as follows:
 *
 * - If after comment and whitespace stripping, the documentation
 *   string of the redeclared method is empty, it will be inherited
 *   from the original method.
 *
 * - Each annotation belonging to the service config (http,
 *   visibility) which is not set in the redeclared method will be
 *   inherited.
 *
 * - If an http annotation is inherited, the path pattern will be
 *   modified as follows. Any version prefix will be replaced by the
 *   version of the including interface plus the [root][] path if
 *   specified.
 *
 * Example of a simple mixin:
 *
 *     package google.acl.v1;
 *     service AccessControl {
 *       // Get the underlying ACL object.
 *       rpc GetAcl(GetAclRequest) returns (Acl) {
 *         option (google.api.http).get = "/v1/{resource=**}:getAcl";
 *       }
 *     }
 *
 *     package google.storage.v2;
 *     service Storage {
 *       rpc GetAcl(GetAclRequest) returns (Acl);
 *
 *       // Get a data record.
 *       rpc GetData(GetDataRequest) returns (Data) {
 *         option (google.api.http).get = "/v2/{resource=**}";
 *       }
 *     }
 *
 * Example of a mixin configuration:
 *
 *     apis:
 *     - name: google.storage.v2.Storage
 *       mixins:
 *       - name: google.acl.v1.AccessControl
 *
 * The mixin construct implies that all methods in `AccessControl` are
 * also declared with same name and request/response types in
 * `Storage`. A documentation generator or annotation processor will
 * see the effective `Storage.GetAcl` method after inherting
 * documentation and annotations as follows:
 *
 *     service Storage {
 *       // Get the underlying ACL object.
 *       rpc GetAcl(GetAclRequest) returns (Acl) {
 *         option (google.api.http).get = "/v2/{resource=**}:getAcl";
 *       }
 *       ...
 *     }
 *
 * Note how the version in the path pattern changed from `v1` to `v2`.
 *
 * If the `root` field in the mixin is specified, it should be a
 * relative path under which inherited HTTP paths are placed. Example:
 *
 *     apis:
 *     - name: google.storage.v2.Storage
 *       mixins:
 *       - name: google.acl.v1.AccessControl
 *         root: acls
 *
 * This implies the following inherited HTTP annotation:
 *
 *     service Storage {
 *       // Get the underlying ACL object.
 *       rpc GetAcl(GetAclRequest) returns (Acl) {
 *         option (google.api.http).get = "/v2/acls/{resource=**}:getAcl";
 *       }
 *       ...
 *     }
 *
 * @generated from message google.protobuf.Mixin
 */
class Mixin extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The fully qualified name of the interface which is included.
         *
         * @generated from field: string name = 1;
         */
        this.name = "";
        /**
         * If non-empty specifies a path under which inherited HTTP paths
         * are rooted.
         *
         * @generated from field: string root = 2;
         */
        this.root = "";
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new Mixin().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Mixin().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Mixin().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Mixin, a, b);
    }
}
exports.Mixin = Mixin;
Mixin.runtime = proto3_js_1.proto3;
Mixin.typeName = "google.protobuf.Mixin";
Mixin.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "root", kind: "scalar", T: 9 /* ScalarType.STRING */ },
]);
