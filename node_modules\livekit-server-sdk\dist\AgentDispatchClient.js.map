{"version": 3, "sources": ["../src/AgentDispatchClient.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport {\n  AgentDispatch,\n  CreateAgentDispatchRequest,\n  DeleteAgentDispatchRequest,\n  ListAgentDispatchRequest,\n  ListAgentDispatchResponse,\n} from '@livekit/protocol';\nimport { ServiceBase } from './ServiceBase.js';\nimport { type Rpc, TwirpRpc, livekitPackage } from './TwirpRPC.js';\n\ninterface CreateDispatchOptions {\n  // any custom data to send along with the job.\n  // note: this is different from room and participant metadata\n  metadata?: string;\n}\n\nconst svc = 'AgentDispatchService';\n\n/**\n * Client to access Agent APIs\n */\nexport class AgentDispatchClient extends ServiceBase {\n  private readonly rpc: Rpc;\n\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host: string, apiKey?: string, secret?: string) {\n    super(apiKey, secret);\n    this.rpc = new TwirpRpc(host, livekitPackage);\n  }\n\n  /**\n   * Create an explicit dispatch for an agent to join a room. To use explicit\n   * dispatch, your agent must be registered with an `agentName`.\n   * @param roomName - name of the room to dispatch to\n   * @param agentName - name of the agent to dispatch\n   * @param options - optional metadata to send along with the dispatch\n   * @returns the dispatch that was created\n   */\n  async createDispatch(\n    roomName: string,\n    agentName: string,\n    options?: CreateDispatchOptions,\n  ): Promise<AgentDispatch> {\n    const req = new CreateAgentDispatchRequest({\n      room: roomName,\n      agentName,\n      metadata: options?.metadata,\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'CreateDispatch',\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }),\n    );\n    return AgentDispatch.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Delete an explicit dispatch for an agent in a room.\n   * @param dispatchId - id of the dispatch to delete\n   * @param roomName - name of the room the dispatch is for\n   */\n  async deleteDispatch(dispatchId: string, roomName: string): Promise<void> {\n    const req = new DeleteAgentDispatchRequest({\n      dispatchId,\n      room: roomName,\n    }).toJson();\n    await this.rpc.request(\n      svc,\n      'DeleteDispatch',\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }),\n    );\n  }\n\n  /**\n   * Get an Agent dispatch by ID\n   * @param dispatchId - id of the dispatch to get\n   * @param roomName - name of the room the dispatch is for\n   * @returns the dispatch that was found, or undefined if not found\n   */\n  async getDispatch(dispatchId: string, roomName: string): Promise<AgentDispatch | undefined> {\n    const req = new ListAgentDispatchRequest({\n      dispatchId,\n      room: roomName,\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'ListDispatch',\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }),\n    );\n    const res = ListAgentDispatchResponse.fromJson(data, { ignoreUnknownFields: true });\n    if (res.agentDispatches.length === 0) {\n      return undefined;\n    }\n    return res.agentDispatches[0];\n  }\n\n  /**\n   * List all agent dispatches for a room\n   * @param roomName - name of the room to list dispatches for\n   * @returns the list of dispatches\n   */\n  async listDispatch(roomName: string): Promise<AgentDispatch[]> {\n    const req = new ListAgentDispatchRequest({\n      room: roomName,\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      'ListDispatch',\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }),\n    );\n    const res = ListAgentDispatchResponse.fromJson(data, { ignoreUnknownFields: true });\n    return res.agentDispatches;\n  }\n}\n"], "mappings": "AAGA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,mBAAmB;AAC5B,SAAmB,UAAU,sBAAsB;AAQnD,MAAM,MAAM;AAKL,MAAM,4BAA4B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnD,YAAY,MAAc,QAAiB,QAAiB;AAC1D,UAAM,QAAQ,MAAM;AACpB,SAAK,MAAM,IAAI,SAAS,MAAM,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eACJ,UACA,WACA,SACwB;AACxB,UAAM,MAAM,IAAI,2BAA2B;AAAA,MACzC,MAAM;AAAA,MACN;AAAA,MACA,UAAU,mCAAS;AAAA,IACrB,CAAC,EAAE,OAAO;AACV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,SAAS,CAAC;AAAA,IAC3D;AACA,WAAO,cAAc,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,YAAoB,UAAiC;AACxE,UAAM,MAAM,IAAI,2BAA2B;AAAA,MACzC;AAAA,MACA,MAAM;AAAA,IACR,CAAC,EAAE,OAAO;AACV,UAAM,KAAK,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,SAAS,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,YAAY,YAAoB,UAAsD;AAC1F,UAAM,MAAM,IAAI,yBAAyB;AAAA,MACvC;AAAA,MACA,MAAM;AAAA,IACR,CAAC,EAAE,OAAO;AACV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,SAAS,CAAC;AAAA,IAC3D;AACA,UAAM,MAAM,0BAA0B,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAClF,QAAI,IAAI,gBAAgB,WAAW,GAAG;AACpC,aAAO;AAAA,IACT;AACA,WAAO,IAAI,gBAAgB,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAAa,UAA4C;AAC7D,UAAM,MAAM,IAAI,yBAAyB;AAAA,MACvC,MAAM;AAAA,IACR,CAAC,EAAE,OAAO;AACV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,WAAW,MAAM,MAAM,SAAS,CAAC;AAAA,IAC3D;AACA,UAAM,MAAM,0BAA0B,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAClF,WAAO,IAAI;AAAA,EACb;AACF;", "names": []}