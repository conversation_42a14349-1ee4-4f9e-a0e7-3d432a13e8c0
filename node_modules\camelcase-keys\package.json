{"name": "camelcase-keys", "version": "9.1.3", "description": "Convert object keys to camel case", "license": "MIT", "repository": "sindresorhus/camelcase-keys", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha bench/bench.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["map", "object", "key", "keys", "value", "values", "iterate", "camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case", "deep", "recurse", "recursive"], "dependencies": {"camelcase": "^8.0.0", "map-obj": "5.0.0", "quick-lru": "^6.1.1", "type-fest": "^4.3.2"}, "devDependencies": {"ava": "^5.3.1", "matcha": "^0.7.0", "tsd": "^0.29.0", "xo": "^0.56.0"}, "xo": {"overrides": [{"files": "bench/bench.js", "rules": {"import/no-unresolved": "off"}}]}}