import type { <PERSON><PERSON><PERSON><PERSON>, Desc<PERSON>essage, DescOneof } from "../descriptor-set.js";
type DescWkt = {
    typeName: "google.protobuf.Any";
    typeUrl: Desc<PERSON>ield;
    value: Des<PERSON><PERSON><PERSON>;
} | {
    typeName: "google.protobuf.Timestamp";
    seconds: <PERSON><PERSON><PERSON><PERSON>;
    nanos: <PERSON><PERSON><PERSON><PERSON>;
} | {
    typeName: "google.protobuf.Duration";
    seconds: <PERSON><PERSON><PERSON><PERSON>;
    nanos: DescField;
} | {
    typeName: "google.protobuf.Struct";
    fields: Desc<PERSON>ield & {
        fieldKind: "map";
    };
} | {
    typeName: "google.protobuf.Value";
    kind: DescOneof;
    nullValue: DescField & {
        fieldKind: "enum";
    };
    numberValue: DescField;
    stringValue: DescField;
    boolValue: DescField;
    structValue: DescField & {
        fieldKind: "message";
    };
    listValue: DescField & {
        fieldKind: "message";
    };
} | {
    typeName: "google.protobuf.ListValue";
    values: DescField & {
        fieldKind: "message";
    };
} | {
    typeName: "google.protobuf.FieldMask";
    paths: Desc<PERSON>ield;
} | {
    typeName: "google.protobuf.DoubleValue";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.FloatValue";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.Int64Value";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.UInt64Value";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.Int32Value";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.UInt32Value";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.BoolValue";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.StringValue";
    value: DescField & {
        fieldKind: "scalar";
    };
} | {
    typeName: "google.protobuf.BytesValue";
    value: DescField & {
        fieldKind: "scalar";
    };
};
/**
 * @deprecated please use reifyWkt from @bufbuild/protoplugin/ecmascript instead
 *
 * Reifies a given DescMessage into a more concrete object representing its
 * respective well-known type.  The returned object will contain properties
 * representing the WKT's defined fields.
 *
 * Useful during code generation when immediate access to a particular field
 * is needed without having to search the object's typename and DescField list.
 *
 * Returns undefined if the WKT cannot be completely constructed via the
 * DescMessage.
 */
export declare function reifyWkt(message: DescMessage): DescWkt | undefined;
export {};
