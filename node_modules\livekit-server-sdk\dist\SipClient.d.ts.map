{"version": 3, "file": "SipClient.d.ts", "sourceRoot": "", "sources": ["../src/SipClient.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EACV,UAAU,EACV,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EACjB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAgBL,eAAe,EAGf,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,kBAAkB,EAClB,YAAY,EACZ,YAAY,EAKb,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAM/C;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B;AACD,MAAM,WAAW,4BAA4B;IAC3C,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,mDAAmD;IACnD,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC5B,iDAAiD;IACjD,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;IAC1B,+CAA+C;IAC/C,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,+CAA+C;IAC/C,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,mBAAmB,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IAEhD,cAAc,CAAC,EAAE,gBAAgB,CAAC;IAClC,YAAY,CAAC,EAAE,OAAO,CAAC;CACxB;AACD,MAAM,WAAW,6BAA6B;IAC5C,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,YAAY,CAAC;IACxB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,+CAA+C;IAC/C,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,+CAA+C;IAC/C,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,mBAAmB,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IAEhD,cAAc,CAAC,EAAE,gBAAgB,CAAC;CACnC;AAED,MAAM,WAAW,qBAAqB;IACpC,IAAI,EAAE,QAAQ,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,YAAY,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,4BAA4B;IAC3C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IACpB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACvC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,iBAAiB,CAAC;CAChC;AAED,MAAM,WAAW,2BAA2B;IAC1C,uEAAuE;IACvE,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,+CAA+C;IAC/C,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,uCAAuC;IACvC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,qDAAqD;IACrD,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,uDAAuD;IACvD,qBAAqB,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IAClD;2DACuD;IACvD,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,6CAA6C;IAC7C,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,iGAAiG;IACjG,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,sHAAsH;IACtH,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,4FAA4F;IAC5F,cAAc,CAAC,EAAE,gBAAgB,CAAC;IAClC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,oDAAoD;IACpD,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,wCAAwC;IACxC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,0EAA0E;IAC1E,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,6EAA6E;IAC7E,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,iHAAiH;IACjH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,0BAA0B;IACzC,0BAA0B;IAC1B,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,6LAA6L;IAC7L,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,mFAAmF;IACnF,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACrB;AAED,MAAM,WAAW,mBAAmB;IAClC,0BAA0B;IAC1B,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,gMAAgM;IAChM,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IACpB,mFAAmF;IACnF,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;CACpB;AAED,MAAM,WAAW,4BAA4B;IAC3C,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,IAAI,CAAC,EAAE,eAAe,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACxC;AAED,MAAM,WAAW,4BAA4B;IAC3C,OAAO,CAAC,EAAE,UAAU,CAAC;IACrB,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAC9B,cAAc,CAAC,EAAE,UAAU,CAAC;IAC5B,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,6BAA6B;IAC5C,OAAO,CAAC,EAAE,UAAU,CAAC;IACrB,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAC9B,cAAc,CAAC,EAAE,UAAU,CAAC;IAC5B,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,6BAA6B;IAC5C,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACrC;AAED;;GAEG;AACH,qBAAa,SAAU,SAAQ,WAAW;IACxC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAM;IAE1B;;;;OAIG;gBACS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;IAK1D;;;;OAIG;IACG,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,qBAAqB,GAAG,OAAO,CAAC,YAAY,CAAC;IA6CzF;;;;;;;OAOG;IACG,qBAAqB,CACzB,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,MAAM,EAAE,EACjB,IAAI,CAAC,EAAE,4BAA4B,GAClC,OAAO,CAAC,mBAAmB,CAAC;IA6B/B;;;;;;;;OAQG;IACG,sBAAsB,CAC1B,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EAAE,EACjB,IAAI,CAAC,EAAE,6BAA6B,GACnC,OAAO,CAAC,oBAAoB,CAAC;IAgChC;;OAEG;IACG,YAAY,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAWlD;;;;;OAKG;IACG,mBAAmB,CAAC,IAAI,GAAE,mBAAwB,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAW9F;;;;;OAKG;IACG,oBAAoB,CAAC,IAAI,GAAE,mBAAwB,GAAG,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAWhG;;;;;OAKG;IACG,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;IAU/D;;;;;;OAMG;IACG,qBAAqB,CACzB,IAAI,EAAE,qBAAqB,GAAG,yBAAyB,EACvD,IAAI,CAAC,EAAE,4BAA4B,GAClC,OAAO,CAAC,mBAAmB,CAAC;IA+C/B;;;;;;OAMG;IACG,qBAAqB,CACzB,iBAAiB,EAAE,MAAM,EACzB,IAAI,EAAE,mBAAmB,GACxB,OAAO,CAAC,mBAAmB,CAAC;IAmB/B;;;;;;;OAOG;IACG,2BAA2B,CAC/B,iBAAiB,EAAE,MAAM,EACzB,MAAM,GAAE,4BAAiC,GACxC,OAAO,CAAC,mBAAmB,CAAC;IAmB/B;;;;;;OAMG;IACG,qBAAqB,CACzB,UAAU,EAAE,MAAM,EAClB,KAAK,EAAE,mBAAmB,GACzB,OAAO,CAAC,mBAAmB,CAAC;IAmB/B;;;;;;;OAOG;IACG,2BAA2B,CAC/B,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,4BAA4B,GACnC,OAAO,CAAC,mBAAmB,CAAC;IAmB/B;;;;;;OAMG;IACG,sBAAsB,CAC1B,UAAU,EAAE,MAAM,EAClB,KAAK,EAAE,oBAAoB,GAC1B,OAAO,CAAC,oBAAoB,CAAC;IAmBhC;;;;;;;OAOG;IACG,4BAA4B,CAChC,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,6BAA6B,GACpC,OAAO,CAAC,oBAAoB,CAAC;IAmBhC;;;;;OAKG;IACG,mBAAmB,CACvB,IAAI,GAAE,0BAA+B,GACpC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAWtC;;;;;OAKG;IACG,qBAAqB,CAAC,iBAAiB,EAAE,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAUpF;;;;;;;;OAQG;IACG,oBAAoB,CACxB,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,IAAI,CAAC,EAAE,2BAA2B,GACjC,OAAO,CAAC,kBAAkB,CAAC;IA2C9B;;;;;;;OAOG;IACG,sBAAsB,CAC1B,QAAQ,EAAE,MAAM,EAChB,mBAAmB,EAAE,MAAM,EAC3B,UAAU,EAAE,MAAM,EAClB,IAAI,CAAC,EAAE,6BAA6B,GACnC,OAAO,CAAC,IAAI,CAAC;CAoBjB"}