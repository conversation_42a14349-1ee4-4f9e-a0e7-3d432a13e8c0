{"version": 3, "sources": ["../src/EgressClient.ts"], "sourcesContent": ["// SPDX-FileCopyrightText: 2024 LiveKit, Inc.\n//\n// SPDX-License-Identifier: Apache-2.0\nimport type {\n  DirectFileOutput,\n  EncodedFileOutput,\n  EncodingOptions,\n  EncodingOptionsPreset,\n  ImageOutput,\n  SegmentedFileOutput,\n  StreamOutput,\n} from '@livekit/protocol';\nimport {\n  AudioMixing,\n  EgressInfo,\n  ListEgressRequest,\n  ListEgressResponse,\n  ParticipantEgressRequest,\n  RoomCompositeEgressRequest,\n  StopEgressRequest,\n  TrackCompositeEgressRequest,\n  TrackEgressRequest,\n  UpdateLayoutRequest,\n  UpdateStreamRequest,\n  WebEgressRequest,\n} from '@livekit/protocol';\nimport { ServiceBase } from './ServiceBase.js';\nimport type { Rpc } from './TwirpRPC.js';\nimport { TwirpRpc, livekitPackage } from './TwirpRPC.js';\n\nconst svc = 'Egress';\n\nexport interface RoomCompositeOptions {\n  /**\n   * egress layout. optional\n   */\n  layout?: string;\n  /**\n   * encoding options or preset. optional\n   */\n  encodingOptions?: EncodingOptionsPreset | EncodingOptions;\n  /**\n   * record audio only. optional\n   */\n  audioOnly?: boolean;\n  /**\n   * record video only. optional\n   */\n  videoOnly?: boolean;\n  /**\n   * custom template url. optional\n   */\n  customBaseUrl?: string;\n  /**\n   * audio mixing options. optional\n   */\n  audioMixing?: AudioMixing;\n}\n\nexport interface WebOptions {\n  /**\n   * encoding options or preset. optional\n   */\n  encodingOptions?: EncodingOptionsPreset | EncodingOptions;\n  /**\n   * record audio only. optional\n   */\n  audioOnly?: boolean;\n  /**\n   * record video only. optional\n   */\n  videoOnly?: boolean;\n  /**\n   * await START_RECORDING chrome log\n   */\n  awaitStartSignal?: boolean;\n}\n\nexport interface ParticipantEgressOptions {\n  /**\n   * true to capture source screenshare and screenshare_audio\n   * false to capture camera and microphone\n   */\n  screenShare?: boolean;\n  /**\n   * encoding options or preset. optional\n   */\n  encodingOptions?: EncodingOptionsPreset | EncodingOptions;\n}\n\nexport interface TrackCompositeOptions {\n  /**\n   * audio track ID\n   */\n  audioTrackId?: string;\n  /**\n   * video track ID\n   */\n  videoTrackId?: string;\n  /**\n   * encoding options or preset. optional\n   */\n  encodingOptions?: EncodingOptionsPreset | EncodingOptions;\n}\n\n/**\n * Used to supply multiple outputs with an egress request\n */\nexport interface EncodedOutputs {\n  file?: EncodedFileOutput | undefined;\n  stream?: StreamOutput | undefined;\n  segments?: SegmentedFileOutput | undefined;\n  images?: ImageOutput | undefined;\n}\n\nexport interface ListEgressOptions {\n  roomName?: string;\n  egressId?: string;\n  active?: boolean;\n}\n\n/**\n * Client to access Egress APIs\n */\nexport class EgressClient extends ServiceBase {\n  private readonly rpc: Rpc;\n\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host: string, apiKey?: string, secret?: string) {\n    super(apiKey, secret);\n    this.rpc = new TwirpRpc(host, livekitPackage);\n  }\n\n  /**\n   * @param roomName - room name\n   * @param output - file or stream output\n   * @param opts - RoomCompositeOptions\n   */\n  async startRoomCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    opts?: RoomCompositeOptions,\n  ): Promise<EgressInfo>;\n  /**\n   * @deprecated use RoomCompositeOptions instead\n   */\n  async startRoomCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    layout?: string,\n    options?: EncodingOptionsPreset | EncodingOptions,\n    audioOnly?: boolean,\n    videoOnly?: boolean,\n    customBaseUrl?: string,\n    audioMixing?: AudioMixing,\n  ): Promise<EgressInfo>;\n  async startRoomCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    optsOrLayout?: RoomCompositeOptions | string,\n    options?: EncodingOptionsPreset | EncodingOptions,\n    audioOnly?: boolean,\n    videoOnly?: boolean,\n    customBaseUrl?: string,\n    audioMixing?: AudioMixing,\n  ): Promise<EgressInfo> {\n    let layout: string | undefined;\n    if (optsOrLayout !== undefined) {\n      if (typeof optsOrLayout === 'string') {\n        layout = optsOrLayout;\n      } else {\n        const opts = <RoomCompositeOptions>optsOrLayout;\n        layout = opts.layout;\n        options = opts.encodingOptions;\n        audioOnly = opts.audioOnly;\n        videoOnly = opts.videoOnly;\n        customBaseUrl = opts.customBaseUrl;\n        audioMixing = opts.audioMixing;\n      }\n    }\n\n    layout ??= '';\n    audioOnly ??= false;\n    videoOnly ??= false;\n    customBaseUrl ??= '';\n    audioMixing ??= AudioMixing.DEFAULT_MIXING;\n\n    const {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    } = this.getOutputParams(output, options);\n\n    const req = new RoomCompositeEgressRequest({\n      roomName,\n      layout,\n      audioOnly,\n      audioMixing,\n      videoOnly,\n      customBaseUrl,\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'StartRoomCompositeEgress',\n      req,\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param url - url\n   * @param output - file or stream output\n   * @param opts - WebOptions\n   */\n  async startWebEgress(\n    url: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    opts?: WebOptions,\n  ): Promise<EgressInfo> {\n    const audioOnly = opts?.audioOnly || false;\n    const videoOnly = opts?.videoOnly || false;\n    const awaitStartSignal = opts?.awaitStartSignal || false;\n    const {\n      output: legacyOutput,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    } = this.getOutputParams(output, opts?.encodingOptions);\n\n    const req = new WebEgressRequest({\n      url,\n      audioOnly,\n      videoOnly,\n      awaitStartSignal,\n      output: legacyOutput,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'StartWebEgress',\n      req,\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * Export a participant's audio and video tracks,\n   *\n   * @param roomName - room name\n   * @param output - one or more outputs\n   * @param opts - ParticipantEgressOptions\n   */\n  async startParticipantEgress(\n    roomName: string,\n    identity: string,\n    output: EncodedOutputs,\n    opts?: ParticipantEgressOptions,\n  ): Promise<EgressInfo> {\n    const { options, fileOutputs, streamOutputs, segmentOutputs, imageOutputs } =\n      this.getOutputParams(output, opts?.encodingOptions);\n    const req = new ParticipantEgressRequest({\n      roomName,\n      identity,\n      screenShare: opts?.screenShare ?? false,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'StartParticipantEgress',\n      req,\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param roomName - room name\n   * @param output - file or stream output\n   * @param opts - TrackCompositeOptions\n   */\n  async startTrackCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    opts?: TrackCompositeOptions,\n  ): Promise<EgressInfo>;\n  /**\n   * @deprecated use TrackCompositeOptions instead\n   */\n  async startTrackCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    audioTrackId?: string,\n    videoTrackId?: string,\n    options?: EncodingOptionsPreset | EncodingOptions,\n  ): Promise<EgressInfo>;\n  async startTrackCompositeEgress(\n    roomName: string,\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    optsOrAudioTrackId?: TrackCompositeOptions | string,\n    videoTrackId?: string,\n    options?: EncodingOptionsPreset | EncodingOptions,\n  ): Promise<EgressInfo> {\n    let audioTrackId: string | undefined;\n    if (optsOrAudioTrackId !== undefined) {\n      if (typeof optsOrAudioTrackId === 'string') {\n        audioTrackId = optsOrAudioTrackId;\n      } else {\n        const opts = <TrackCompositeOptions>optsOrAudioTrackId;\n        audioTrackId = opts.audioTrackId;\n        videoTrackId = opts.videoTrackId;\n        options = opts.encodingOptions;\n      }\n    }\n\n    audioTrackId ??= '';\n    videoTrackId ??= '';\n\n    const {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    } = this.getOutputParams(output, options);\n    const req = new TrackCompositeEgressRequest({\n      roomName,\n      audioTrackId,\n      videoTrackId,\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'StartTrackCompositeEgress',\n      req,\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private isEncodedOutputs(output: any): output is EncodedOutputs {\n    return (\n      (<EncodedOutputs>output).file !== undefined ||\n      (<EncodedOutputs>output).stream !== undefined ||\n      (<EncodedOutputs>output).segments !== undefined ||\n      (<EncodedOutputs>output).images !== undefined\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private isEncodedFileOutput(output: any): output is EncodedFileOutput {\n    return (\n      (<EncodedFileOutput>output).filepath !== undefined ||\n      (<EncodedFileOutput>output).fileType !== undefined\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private isSegmentedFileOutput(output: any): output is SegmentedFileOutput {\n    return (\n      (<SegmentedFileOutput>output).filenamePrefix !== undefined ||\n      (<SegmentedFileOutput>output).playlistName !== undefined ||\n      (<SegmentedFileOutput>output).filenameSuffix !== undefined\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private isStreamOutput(output: any): output is StreamOutput {\n    return (\n      (<StreamOutput>output).protocol !== undefined || (<StreamOutput>output).urls !== undefined\n    );\n  }\n\n  private getOutputParams(\n    output: EncodedOutputs | EncodedFileOutput | StreamOutput | SegmentedFileOutput,\n    opts?: EncodingOptionsPreset | EncodingOptions,\n  ) {\n    let file: EncodedFileOutput | undefined;\n    let fileOutputs: Array<EncodedFileOutput> | undefined;\n    let stream: StreamOutput | undefined;\n    let streamOutputs: Array<StreamOutput> | undefined;\n    let segments: SegmentedFileOutput | undefined;\n    let segmentOutputs: Array<SegmentedFileOutput> | undefined;\n    let imageOutputs: Array<ImageOutput> | undefined;\n\n    if (this.isEncodedOutputs(output)) {\n      if (output.file !== undefined) {\n        fileOutputs = [output.file];\n      }\n      if (output.stream !== undefined) {\n        streamOutputs = [output.stream];\n      }\n      if (output.segments !== undefined) {\n        segmentOutputs = [output.segments];\n      }\n      if (output.images !== undefined) {\n        imageOutputs = [output.images];\n      }\n    } else if (this.isEncodedFileOutput(output)) {\n      file = output;\n      fileOutputs = [file];\n    } else if (this.isSegmentedFileOutput(output)) {\n      segments = output;\n      segmentOutputs = [segments];\n    } else if (this.isStreamOutput(output)) {\n      stream = output;\n      streamOutputs = [stream];\n    }\n\n    let legacyOutput:\n      | {\n          value: EncodedFileOutput;\n          case: 'file';\n        }\n      | {\n          value: StreamOutput;\n          case: 'stream';\n        }\n      | {\n          value: SegmentedFileOutput;\n          case: 'segments';\n        }\n      | undefined;\n\n    if (file) {\n      legacyOutput = {\n        case: 'file',\n        value: file,\n      };\n    } else if (stream) {\n      legacyOutput = {\n        case: 'stream',\n        value: stream,\n      };\n    } else if (segments) {\n      legacyOutput = {\n        case: 'segments',\n        value: segments,\n      };\n    }\n    let egressOptions:\n      | {\n          value: EncodingOptionsPreset;\n          case: 'preset';\n        }\n      | {\n          value: EncodingOptions;\n          case: 'advanced';\n        }\n      | undefined;\n    if (opts) {\n      if (typeof opts === 'number') {\n        egressOptions = {\n          case: 'preset',\n          value: opts,\n        };\n      } else {\n        egressOptions = {\n          case: 'advanced',\n          value: <EncodingOptions>opts,\n        };\n      }\n    }\n\n    return {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs,\n    };\n  }\n\n  /**\n   * @param roomName - room name\n   * @param output - file or websocket output\n   * @param trackId - track Id\n   */\n  async startTrackEgress(\n    roomName: string,\n    output: DirectFileOutput | string,\n    trackId: string,\n  ): Promise<EgressInfo> {\n    let legacyOutput:\n      | {\n          value: DirectFileOutput;\n          case: 'file';\n        }\n      | {\n          value: string;\n          case: 'websocketUrl';\n        }\n      | undefined;\n\n    if (typeof output === 'string') {\n      legacyOutput = {\n        case: 'websocketUrl',\n        value: output,\n      };\n    } else {\n      legacyOutput = {\n        case: 'file',\n        value: output,\n      };\n    }\n\n    const req = new TrackEgressRequest({\n      roomName,\n      trackId,\n      output: legacyOutput,\n    }).toJson();\n\n    const data = await this.rpc.request(\n      svc,\n      'StartTrackEgress',\n      req,\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param egressId -\n   * @param layout -\n   */\n  async updateLayout(egressId: string, layout: string): Promise<EgressInfo> {\n    const data = await this.rpc.request(\n      svc,\n      'UpdateLayout',\n      new UpdateLayoutRequest({ egressId, layout }).toJson(),\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param egressId -\n   * @param addOutputUrls -\n   * @param removeOutputUrls -\n   */\n  async updateStream(\n    egressId: string,\n    addOutputUrls?: string[],\n    removeOutputUrls?: string[],\n  ): Promise<EgressInfo> {\n    addOutputUrls ??= [];\n    removeOutputUrls ??= [];\n\n    const data = await this.rpc.request(\n      svc,\n      'UpdateStream',\n      new UpdateStreamRequest({ egressId, addOutputUrls, removeOutputUrls }).toJson(),\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n\n  /**\n   * @param options - options to filter listed Egresses, by default returns all\n   * Egress instances\n   */\n  async listEgress(options?: ListEgressOptions): Promise<Array<EgressInfo>>;\n  /**\n   * @deprecated use `listEgress(options?: ListEgressOptions)` instead\n   * @param roomName - list egress for one room only\n   */\n  async listEgress(roomName?: string): Promise<Array<EgressInfo>>;\n  /**\n   * @param roomName - list egress for one room only\n   */\n  async listEgress(options?: string | ListEgressOptions): Promise<Array<EgressInfo>> {\n    let req: Partial<ListEgressRequest> = {};\n    if (typeof options === 'string') {\n      req.roomName = options;\n    } else if (options !== undefined) {\n      req = options;\n    }\n\n    const data = await this.rpc.request(\n      svc,\n      'ListEgress',\n      new ListEgressRequest(req).toJson(),\n      await this.authHeader({ roomRecord: true }),\n    );\n    return ListEgressResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n\n  /**\n   * @param egressId -\n   */\n  async stopEgress(egressId: string): Promise<EgressInfo> {\n    const data = await this.rpc.request(\n      svc,\n      'StopEgress',\n      new StopEgressRequest({ egressId }).toJson(),\n      await this.authHeader({ roomRecord: true }),\n    );\n    return EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,sBAaO;AACP,yBAA4B;AAE5B,sBAAyC;AAEzC,MAAM,MAAM;AA8FL,MAAM,qBAAqB,+BAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ5C,YAAY,MAAc,QAAiB,QAAiB;AAC1D,UAAM,QAAQ,MAAM;AACpB,SAAK,MAAM,IAAI,yBAAS,MAAM,8BAAc;AAAA,EAC9C;AAAA,EAyBA,MAAM,yBACJ,UACA,QACA,cACA,SACA,WACA,WACA,eACA,aACqB;AACrB,QAAI;AACJ,QAAI,iBAAiB,QAAW;AAC9B,UAAI,OAAO,iBAAiB,UAAU;AACpC,iBAAS;AAAA,MACX,OAAO;AACL,cAAM,OAA6B;AACnC,iBAAS,KAAK;AACd,kBAAU,KAAK;AACf,oBAAY,KAAK;AACjB,oBAAY,KAAK;AACjB,wBAAgB,KAAK;AACrB,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF;AAEA,eAAW;AACX,kBAAc;AACd,kBAAc;AACd,sBAAkB;AAClB,oBAAgB,4BAAY;AAE5B,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,QAAQ,OAAO;AAExC,UAAM,MAAM,IAAI,2CAA2B;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eACJ,KACA,QACA,MACqB;AACrB,UAAM,aAAY,6BAAM,cAAa;AACrC,UAAM,aAAY,6BAAM,cAAa;AACrC,UAAM,oBAAmB,6BAAM,qBAAoB;AACnD,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,QAAQ,6BAAM,eAAe;AAEtD,UAAM,MAAM,IAAI,iCAAiB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,uBACJ,UACA,UACA,QACA,MACqB;AACrB,UAAM,EAAE,SAAS,aAAa,eAAe,gBAAgB,aAAa,IACxE,KAAK,gBAAgB,QAAQ,6BAAM,eAAe;AACpD,UAAM,MAAM,IAAI,yCAAyB;AAAA,MACvC;AAAA,MACA;AAAA,MACA,cAAa,6BAAM,gBAAe;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA,EAsBA,MAAM,0BACJ,UACA,QACA,oBACA,cACA,SACqB;AACrB,QAAI;AACJ,QAAI,uBAAuB,QAAW;AACpC,UAAI,OAAO,uBAAuB,UAAU;AAC1C,uBAAe;AAAA,MACjB,OAAO;AACL,cAAM,OAA8B;AACpC,uBAAe,KAAK;AACpB,uBAAe,KAAK;AACpB,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF;AAEA,qBAAiB;AACjB,qBAAiB;AAEjB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,QAAQ,OAAO;AACxC,UAAM,MAAM,IAAI,4CAA4B;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA,EAGQ,iBAAiB,QAAuC;AAC9D,WACmB,OAAQ,SAAS,UACjB,OAAQ,WAAW,UACnB,OAAQ,aAAa,UACrB,OAAQ,WAAW;AAAA,EAExC;AAAA;AAAA,EAGQ,oBAAoB,QAA0C;AACpE,WACsB,OAAQ,aAAa,UACrB,OAAQ,aAAa;AAAA,EAE7C;AAAA;AAAA,EAGQ,sBAAsB,QAA4C;AACxE,WACwB,OAAQ,mBAAmB,UAC3B,OAAQ,iBAAiB,UACzB,OAAQ,mBAAmB;AAAA,EAErD;AAAA;AAAA,EAGQ,eAAe,QAAqC;AAC1D,WACiB,OAAQ,aAAa,UAA4B,OAAQ,SAAS;AAAA,EAErF;AAAA,EAEQ,gBACN,QACA,MACA;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,QAAI,KAAK,iBAAiB,MAAM,GAAG;AACjC,UAAI,OAAO,SAAS,QAAW;AAC7B,sBAAc,CAAC,OAAO,IAAI;AAAA,MAC5B;AACA,UAAI,OAAO,WAAW,QAAW;AAC/B,wBAAgB,CAAC,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,OAAO,aAAa,QAAW;AACjC,yBAAiB,CAAC,OAAO,QAAQ;AAAA,MACnC;AACA,UAAI,OAAO,WAAW,QAAW;AAC/B,uBAAe,CAAC,OAAO,MAAM;AAAA,MAC/B;AAAA,IACF,WAAW,KAAK,oBAAoB,MAAM,GAAG;AAC3C,aAAO;AACP,oBAAc,CAAC,IAAI;AAAA,IACrB,WAAW,KAAK,sBAAsB,MAAM,GAAG;AAC7C,iBAAW;AACX,uBAAiB,CAAC,QAAQ;AAAA,IAC5B,WAAW,KAAK,eAAe,MAAM,GAAG;AACtC,eAAS;AACT,sBAAgB,CAAC,MAAM;AAAA,IACzB;AAEA,QAAI;AAeJ,QAAI,MAAM;AACR,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,WAAW,QAAQ;AACjB,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,WAAW,UAAU;AACnB,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI;AAUJ,QAAI,MAAM;AACR,UAAI,OAAO,SAAS,UAAU;AAC5B,wBAAgB;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,UACd,MAAM;AAAA,UACN,OAAwB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,iBACJ,UACA,QACA,SACqB;AACrB,QAAI;AAWJ,QAAI,OAAO,WAAW,UAAU;AAC9B,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,MAAM,IAAI,mCAAmB;AAAA,MACjC;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV,CAAC,EAAE,OAAO;AAEV,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa,UAAkB,QAAqC;AACxE,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,oCAAoB,EAAE,UAAU,OAAO,CAAC,EAAE,OAAO;AAAA,MACrD,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aACJ,UACA,eACA,kBACqB;AACrB,sBAAkB,CAAC;AACnB,yBAAqB,CAAC;AAEtB,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,oCAAoB,EAAE,UAAU,eAAe,iBAAiB,CAAC,EAAE,OAAO;AAAA,MAC9E,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,WAAW,SAAkE;AACjF,QAAI,MAAkC,CAAC;AACvC,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,WAAW;AAAA,IACjB,WAAW,YAAY,QAAW;AAChC,YAAM;AAAA,IACR;AAEA,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,kCAAkB,GAAG,EAAE,OAAO;AAAA,MAClC,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,mCAAmB,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,UAAuC;AACtD,UAAM,OAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,IAAI,kCAAkB,EAAE,SAAS,CAAC,EAAE,OAAO;AAAA,MAC3C,MAAM,KAAK,WAAW,EAAE,YAAY,KAAK,CAAC;AAAA,IAC5C;AACA,WAAO,2BAAW,SAAS,MAAM,EAAE,qBAAqB,KAAK,CAAC;AAAA,EAChE;AACF;", "names": []}