// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_room.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";
import { RoomAgentDispatch } from "./livekit_agent_dispatch_pb.js";
import { AutoParticipantEgress, AutoTrackEgress, RoomCompositeEgressRequest } from "./livekit_egress_pb.js";
import { DataPacket_Kind, ParticipantInfo, ParticipantPermission, ParticipantTracks, Room, TrackInfo } from "./livekit_models_pb.js";

/**
 * @generated from message livekit.CreateRoomRequest
 */
export const CreateRoomRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateRoomRequest",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "room_preset", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "empty_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 10, name: "departure_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "max_participants", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "node_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "egress", kind: "message", T: RoomEgress },
    { no: 7, name: "min_playout_delay", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 8, name: "max_playout_delay", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 9, name: "sync_streams", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 13, name: "replay_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 14, name: "agents", kind: "message", T: RoomAgentDispatch, repeated: true },
  ],
);

/**
 * @generated from message livekit.RoomEgress
 */
export const RoomEgress = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomEgress",
  () => [
    { no: 1, name: "room", kind: "message", T: RoomCompositeEgressRequest },
    { no: 3, name: "participant", kind: "message", T: AutoParticipantEgress },
    { no: 2, name: "tracks", kind: "message", T: AutoTrackEgress },
  ],
);

/**
 * @generated from message livekit.RoomAgent
 */
export const RoomAgent = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomAgent",
  () => [
    { no: 1, name: "dispatches", kind: "message", T: RoomAgentDispatch, repeated: true },
  ],
);

/**
 * @generated from message livekit.ListRoomsRequest
 */
export const ListRoomsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListRoomsRequest",
  () => [
    { no: 1, name: "names", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.ListRoomsResponse
 */
export const ListRoomsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListRoomsResponse",
  () => [
    { no: 1, name: "rooms", kind: "message", T: Room, repeated: true },
  ],
);

/**
 * @generated from message livekit.DeleteRoomRequest
 */
export const DeleteRoomRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteRoomRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeleteRoomResponse
 */
export const DeleteRoomResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteRoomResponse",
  [],
);

/**
 * @generated from message livekit.ListParticipantsRequest
 */
export const ListParticipantsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListParticipantsRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListParticipantsResponse
 */
export const ListParticipantsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListParticipantsResponse",
  () => [
    { no: 1, name: "participants", kind: "message", T: ParticipantInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.RoomParticipantIdentity
 */
export const RoomParticipantIdentity = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomParticipantIdentity",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RemoveParticipantResponse
 */
export const RemoveParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RemoveParticipantResponse",
  [],
);

/**
 * @generated from message livekit.MuteRoomTrackRequest
 */
export const MuteRoomTrackRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MuteRoomTrackRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "track_sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "muted", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.MuteRoomTrackResponse
 */
export const MuteRoomTrackResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MuteRoomTrackResponse",
  () => [
    { no: 1, name: "track", kind: "message", T: TrackInfo },
  ],
);

/**
 * @generated from message livekit.UpdateParticipantRequest
 */
export const UpdateParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateParticipantRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "permission", kind: "message", T: ParticipantPermission },
    { no: 5, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
  ],
);

/**
 * @generated from message livekit.UpdateSubscriptionsRequest
 */
export const UpdateSubscriptionsRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateSubscriptionsRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "track_sids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "subscribe", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "participant_tracks", kind: "message", T: ParticipantTracks, repeated: true },
  ],
);

/**
 * empty for now
 *
 * @generated from message livekit.UpdateSubscriptionsResponse
 */
export const UpdateSubscriptionsResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateSubscriptionsResponse",
  [],
);

/**
 * @generated from message livekit.SendDataRequest
 */
export const SendDataRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SendDataRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "data", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
    { no: 3, name: "kind", kind: "enum", T: proto3.getEnumType(DataPacket_Kind) },
    { no: 4, name: "destination_sids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "destination_identities", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "topic", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 7, name: "nonce", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
  ],
);

/**
 * @generated from message livekit.SendDataResponse
 */
export const SendDataResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SendDataResponse",
  [],
);

/**
 * @generated from message livekit.UpdateRoomMetadataRequest
 */
export const UpdateRoomMetadataRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateRoomMetadataRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RoomConfiguration
 */
export const RoomConfiguration = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomConfiguration",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "empty_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "departure_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "max_participants", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "egress", kind: "message", T: RoomEgress },
    { no: 7, name: "min_playout_delay", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 8, name: "max_playout_delay", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 9, name: "sync_streams", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 10, name: "agents", kind: "message", T: RoomAgentDispatch, repeated: true },
  ],
);

/**
 * @generated from message livekit.ForwardParticipantRequest
 */
export const ForwardParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ForwardParticipantRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "destination_room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ForwardParticipantResponse
 */
export const ForwardParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ForwardParticipantResponse",
  [],
);

/**
 * @generated from message livekit.MoveParticipantRequest
 */
export const MoveParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MoveParticipantRequest",
  () => [
    { no: 1, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "destination_room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.MoveParticipantResponse
 */
export const MoveParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MoveParticipantResponse",
  [],
);

